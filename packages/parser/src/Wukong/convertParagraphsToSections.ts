import type { Section } from "../Chat";
import { BufferState, IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { ContentfulSection, parseTaggedContent } from "../parseTaggedContent";
import { ToolCallManager } from "../ToolCallManager";
import { neverReachHere } from "../utils";
import { Paragraph, ParagraphType } from "./Paragraphing";

export const convertParagraphsToSections = (
  paragraphs: IterableBuffer<Paragraph>,
  toolCallManager: ToolCallManager | undefined,
  abortController: AbortController | undefined
): IterableBuffer<Section> => {
  const sections = new LiveBuffer<Section>();

  let lastWukongCall: { name: IterableBuffer<string>; arguments: LiveBuffer<string>; result: LiveBuffer<string> };

  paragraphs.tee({
    enqueue: (paragraph) => {
      switch (paragraph.type) {
        case ParagraphType.Answer:
          const clientMessages = new LiveBuffer<string>();
          processContentfulSections(
            parseTaggedContent(paragraph.value),
            toolCallManager,
            abortController,
            clientMessages,
            sections
          );
          clientMessages.close();
          sections.enqueue({ type: "content", value: clientMessages });
          break;
        case ParagraphType.Action:
          lastWukongCall = {
            name: paragraph.value,
            arguments: new LiveBuffer<string>(),
            result: new LiveBuffer<string>(),
          };
          sections.enqueue({ type: "call", value: lastWukongCall });
          break;
        case ParagraphType.ActionInput:
          if (lastWukongCall) paragraph.value.tee(lastWukongCall.arguments);
          break;
        case ParagraphType.Observation:
          if (lastWukongCall) paragraph.value.tee(lastWukongCall.result);
          break;
        default:
          neverReachHere(paragraph.type);
      }
    },
    close: () => {
      if (lastWukongCall) {
        lastWukongCall.result.closeIfNeeded();
        lastWukongCall.arguments.closeIfNeeded();
      }
      sections.close();
    },
    error: (reason) => {
      if (lastWukongCall) {
        if (lastWukongCall.result.state === BufferState.Open) lastWukongCall.result.error(reason);
        if (lastWukongCall.arguments.state === BufferState.Open) lastWukongCall.arguments.error(reason);
      }
      sections.error(reason);
    },
  });
  return sections;
};

const processContentfulSections = (
  input: IterableBuffer<ContentfulSection>,
  toolCallManager: ToolCallManager | undefined,
  abortController: AbortController | undefined,
  clientMessages: LiveBuffer<string>,
  sections: LiveBuffer<Section>
) => {
  const calls = [];
  input.tee({
    enqueue: (section) => {
      switch (section.type) {
        case "call":
          if (toolCallManager) {
            const value = toolCallManager.invokeTool(section.value, abortController, clientMessages);
            calls.push(value);
          }
          sections.enqueue({
            type: "call",
            value: {
              name: section.value.name,
              arguments: section.value.arguments,
              result: LiveBuffer.closure(["null"]),
            },
          });
          break;
        case "content":
        case "think":
          sections.enqueue(section);
          break;
        default:
          neverReachHere(section);
      }
    },
    error: (reason) => {
      sections.error(reason);
    },
  });
};
