import { ChatBasic } from "../ChatBasic";
import { type ChatMessage, ChatMessageRole, CommonChatOptions, ToolDeclarationFunction } from "../Chat";
import { BufferState, IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { SequenceDigester } from "../SequenceDigest";

import { Paragraph, paragraphing } from "./Paragraphing";
import { convertParagraphsToSections } from "./convertParagraphsToSections";
import { WukongMessage, loadWukongData, WukongOptions, WukongSerializedMessage } from "./loadWukongData";
import { deepCopy } from "../utils";

export interface WukongChatOptions extends Omit<WukongOptions, "additional_messages">, CommonChatOptions {
  additional_messages?: WukongSerializedMessage[];
}

export class WukongChat extends ChatBasic<WukongChatOptions> {
  readonly #additionalMessages: WukongSerializedMessage[];
  readonly #messages: ChatMessage[];
  #abortController?: AbortController;

  constructor(url: string, options: WukongChatOptions & Record<string, unknown>) {
    const { additional_messages, welcome_message, ...restOptions } = options;

    const am: WukongSerializedMessage[] = [];
    const mm: ChatMessage[] = [];
    super(url, { ...deepCopy(restOptions), additional_messages: am }, mm);
    this.#additionalMessages = am;
    this.#messages = mm;

    // Recover chat history from the additional_messages.
    if (additional_messages?.length) {
      for (const message of additional_messages) {
        const { role, content } = WukongMessage.wrap(message);
        switch (role) {
          case "user":
            this.appendContentMessage(ChatMessageRole.User, content);
            break;
          case "assistant":
            this.generateNextAssistant(LiveBuffer.closure([content]));
            break;
          default:
            throw new Error(`Unknown role: ${role}`);
        }
      }
    } else {
      if (typeof welcome_message === "string") {
        this.appendContentMessage(ChatMessageRole.Assistant, welcome_message);
      }
    }
  }

  public async requestIfNeeded() {
    const a = this.#additionalMessages;
    if (a[a.length - 1]?.role === "user") {
      this.#abortController = new AbortController();
      const buffer = loadWukongData(this.url, {
        options: {
          ...this.options,
          additional_messages: this.#additionalMessages,
          // This is a extension property, it never be send to LLM.
          additional_api_tools: undefined,
          additional_mcp_servers: undefined,
          additional_native_tools: undefined,
        },
        signal: this.#abortController.signal,
      });
      buffer.then(
        () => (this.#abortController = undefined),
        () => (this.#abortController = undefined)
      );
      await this.generateNextAssistant(buffer, true);
    }
  }

  public appendContentMessage(role: ChatMessageRole, content: string) {
    if (this.busy) throw new Error("It's busy, please wait for the current response to complete");
    if (this.lastMessage?.role === role) {
      throw new Error("Cannot append a message with the same role with the last message");
    }
    this.#messages.push({
      index: this.#additionalMessages.length,
      role,
      sections: LiveBuffer.closure([{ type: "content", value: LiveBuffer.closure([content]) }]),
    });
    this.#additionalMessages.push(new WukongMessage(role, content));
    this.notify();
  }

  /**
   * NOTE: This method is synchronous if the input buffer is not in Open state.
   */
  private async generateNextAssistant(input: IterableBuffer<string>, needToCall = false) {
    const paragraphs = new LiveBuffer<Paragraph>();
    const digester = new SequenceDigester(() => paragraphing(paragraphs));

    const sections = convertParagraphsToSections(
      paragraphs,
      needToCall ? this.toolCallManager : undefined,
      needToCall ? this.#abortController : undefined
    );

    this.#messages.push({
      role: ChatMessageRole.Assistant,
      index: this.#additionalMessages.length,
      sections,
    });

    this.notify();

    input.tee({
      enqueue: (chunk) => {
        digester.write(chunk);
      },
      close: () => {
        digester.close();
        paragraphs.close();
      },
      error: (reason) => {
        digester.close();
        paragraphs.error(reason);
      },
    });

    try {
      if (input.state === BufferState.Open) await input;
    } finally {
      this.#additionalMessages.push(new WukongMessage("assistant", input.buffer.join("")));
      this.notify();
    }
  }

  public next(content: string) {
    this.appendContentMessage(ChatMessageRole.User, content);
    this.requestIfNeeded();
  }

  public rewindToBefore(message: ChatMessage) {
    this.abort();
    const index = this.#messages.indexOf(message);
    if (index === -1) throw new Error("Message not found");
    this.#additionalMessages.splice(message.index);
    this.#messages.splice(index);
    this.notify();
    this.requestIfNeeded();
  }

  public abort(reason?: Error) {
    this.#abortController?.abort(reason || new Error("Aborted"));
  }

  public registerNativeTool(
    declarationFunction: ToolDeclarationFunction<string>,
    implement: (args: Record<string, unknown>) => Promise<unknown>
  ) {
    this.toolCallManager.registerNativeTool(declarationFunction, implement);
  }

  public registerEnvironmentVariables(variables: Record<string, string>) {
    this.toolCallManager.registerEnvironmentVariables(variables);
  }
}
