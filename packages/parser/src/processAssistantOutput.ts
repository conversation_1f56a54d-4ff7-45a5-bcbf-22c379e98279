import { AssistantOutputItem, LiveToolCall } from "./AssistantOutputItem";
import { Section } from "./Chat";
import { LiveBuffer, IterableBuffer } from "./IterableBuffer";
import { OpenAiMessage } from "./OpenAiMessage";
import { parseTaggedContent } from "./parseTaggedContent";
import { ToolCallManager } from "./ToolCallManager";

export const processAssistantOutput = async (
  output: IterableBuffer<AssistantOutputItem>,
  sections: LiveBuffer<Section>,
  additionalMessages: OpenAiMessage[],
  toolCallManager: ToolCallManager,
  abortController: AbortController
) => {
  const callArray: LiveToolCall[] = [];
  const contentArray: IterableBuffer<string>[] = [];
  const invokings: IterableBuffer<string>[] = [];
  const clientMessages: LiveBuffer<string> = new LiveBuffer();
  let finishReason: string | null = null;
  try {
    for await (const item of output) {
      if (item.type === "call") {
        callArray.push(item.value);
        const value = toolCallManager.invokeTool(item.value.function, abortController, clientMessages);
        sections.enqueue({ type: "call", value });
        invokings.push(value.result);
      } else if (item.type === "content") {
        contentArray.push(item.value);
        for await (const contentfulSection of parseTaggedContent(item.value)) {
          if (contentfulSection.type === "call") {
            const value = toolCallManager.invokeTool(contentfulSection.value, abortController, clientMessages);
            sections.enqueue({ type: "call", value });
            invokings.push(value.result);
          } else sections.enqueue(contentfulSection);
        }
      } else if (item.type === "finish") {
        finishReason = item.value;
      }
    }
    clientMessages.tee({
      enqueue: (content) => {
        sections.enqueue({ type: "content", value: LiveBuffer.closure([content]) });
      },
    });
  } finally {
    additionalMessages.push({
      role: "assistant" as const,
      content: contentArray.map((buffer) => buffer.buffer.join("")).join("\n\n"),
      tool_calls: callArray.length
        ? callArray.map((call) => ({
            ...call,
            function: {
              name: call.function.name.buffer.join(""),
              arguments: call.function.arguments.buffer.join(""),
            },
          }))
        : undefined,
    });
    for (let i = 0; i < invokings.length; i++) {
      const content = (await invokings[i]).join("");
      additionalMessages.push({ role: "tool", tool_call_id: callArray[i]?.id, content });
    }
    clientMessages.close();
    for (const content of clientMessages.buffer) {
      additionalMessages.push({ role: "client", content });
    }
  }

  return invokings.length > 0 || finishReason === "tool_calls";
};
