import { LiveBuffer } from "../IterableBuffer";
import { ChatMessage, ChatMessageRole, CommonChatOptions, Section, ToolDeclarationFunction } from "../Chat";
import { convertOamToMessages, OpenAiMessage } from "../OpenAiMessage";
import { HaitunOptions, loadHaitunData } from "./loadHaitunData";
import { ChatBasic } from "../ChatBasic";
import { PartialOptional, deepCopy } from "../utils";
import { processAssistantOutput } from "../processAssistantOutput";

export interface HaitunChatOptions extends Omit<HaitunOptions, "tools">, CommonChatOptions {
  /**
   * The first system message of the chat.
   * NOTE: If additional_messages is not empty, this property will be ignored.
   *       Because it will be append into the additional_messages.
   */
  system_message?: string;
}

export class HaitunChat extends ChatBasic<HaitunChatOptions> {
  #abortController?: AbortController;
  #additionalMessages: OpenAiMessage[];
  readonly #messages: ChatMessage[];

  constructor(url: string, options: PartialOptional<HaitunChatOptions, "messages"> & Record<string, unknown>) {
    const { messages: additional_messages, tools, system_message, welcome_message, ...restOptions } = options;

    if (tools)
      throw new Error("The `tools` parameter is not allowed in HaitunChatOptions, use `registerTool` instead.");

    const am: OpenAiMessage[] = [];
    const mm: ChatMessage[] = [];
    super(url, { ...deepCopy(restOptions), messages: am }, mm);
    this.#additionalMessages = am;
    this.#messages = mm;

    // Convert messages to groups.
    // This code is used to recover the history of a conversation.
    if (additional_messages && additional_messages.length > 0) {
      this.#messages.push(...convertOamToMessages(additional_messages));
      this.#additionalMessages.push(...additional_messages);
    } else {
      if (typeof system_message === "string") {
        this.appendContentMessage(ChatMessageRole.System, system_message);
      }
      if (typeof welcome_message === "string") {
        this.appendContentMessage(ChatMessageRole.Assistant, welcome_message);
      }
    }

    this.notify();
  }

  public async requestIfNeeded() {
    if (this.#additionalMessages[this.#additionalMessages.length - 1]?.role === "user") {
      await this.generateNextAssistant();
    }
  }

  public appendContentMessage(role: ChatMessageRole, content: string) {
    if (this.busy) throw new Error("It's busy, please wait for the current response to complete");
    if (this.lastMessage?.role === role) {
      throw new Error("Cannot append a message with the same role with the last message");
    }
    if (role !== "system") {
      this.#messages.push({
        index: this.#additionalMessages.length,
        role,
        sections: LiveBuffer.closure([{ type: "content", value: LiveBuffer.closure([content]) }]),
      });
    }
    this.#additionalMessages.push({ role, content });
    this.notify();
  }

  /**
   * NOTE: This method is synchronous if the input buffer is not in Open state.
   */
  private async generateNextAssistant() {
    const sections = new LiveBuffer<Section>();
    const group: ChatMessage = {
      index: this.#additionalMessages.length,
      role: ChatMessageRole.Assistant,
      sections,
    };
    this.#messages.push(group);
    this.notify();

    try {
      for (;;) {
        if (!this.#abortController) this.#abortController = new AbortController();
        if (this.toolCallManager.mcpServersInitializing) await this.toolCallManager.mcpServersInitializing;
        const output = loadHaitunData(this.url, {
          options: {
            ...this.options,
            messages: this.#additionalMessages,
            tools: this.toolCallManager.getToolDeclarations(),
            // This is a extension property, it never be send to LLM.
            additional_api_tools: undefined,
            additional_mcp_servers: undefined,
            additional_native_tools: undefined,
          },
          signal: this.#abortController.signal,
        });

        if (
          await processAssistantOutput(
            output,
            sections,
            this.#additionalMessages,
            this.toolCallManager,
            this.#abortController
          )
        ) {
          continue;
        }
        break;
      }
    } catch (error) {
      sections.error(error);
    } finally {
      sections.closeIfNeeded();
      this.#abortController = undefined;
      this.notify();
    }
  }

  public next(content: string) {
    this.appendContentMessage(ChatMessageRole.User, content);
    this.requestIfNeeded();
  }

  public rewindToBefore(message: ChatMessage) {
    this.abort();
    const index = this.#messages.indexOf(message);
    if (index === -1) throw new Error("Message not found");
    this.#additionalMessages.splice(message.index);
    this.#messages.splice(index);
    this.notify();
    this.requestIfNeeded();
  }

  public abort(reason?: Error) {
    this.#abortController?.abort(reason || new Error("Aborted"));
  }

  public registerTool<T extends string>(
    declarationFunction: ToolDeclarationFunction<T>,
    implement: (args: Record<T, unknown>) => Promise<unknown>
  ) {
    this.toolCallManager.registerTool(declarationFunction, implement);
  }

  public registerNativeTool(
    declarationFunction: ToolDeclarationFunction,
    implement: (args: Record<string, unknown>) => Promise<unknown>
  ) {
    this.toolCallManager.registerNativeTool(declarationFunction, implement);
  }

  public registerEnvironmentVariables(variables: Record<string, string>) {
    this.toolCallManager.registerEnvironmentVariables(variables);
  }
}
