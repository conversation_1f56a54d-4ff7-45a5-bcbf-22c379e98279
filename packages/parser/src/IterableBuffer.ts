export type ReadingController<T> = {
  enqueue?: (item: T) => void;
  close?: () => void;
  error?: (reason: unknown) => void;
};

export interface IterableBuffer<T> extends AsyncIterable<T>, PromiseLike<T[]> {
  close(): unknown;
  readonly buffer: readonly T[];
  readonly reason: Error | object | null;
  readonly state: BufferState;

  stream(): ReadableStream<T>;

  tee(controller: ReadingController<T>): void;
  untee(controller: ReadingController<T>): void;
}

export enum BufferState {
  Open = 0,
  Closed = 1,
  Error = 2,
}

export class LiveBuffer<T> implements IterableBuffer<T>, ReadingController<T> {
  public readonly buffer: readonly T[];
  public readonly reason: Error | object | null;
  public readonly state: BufferState;
  readonly #buffer: T[];
  readonly #waitings: ReadingController<T>[];

  constructor() {
    this.state = BufferState.Open;
    this.reason = null;
    this.#buffer = [];
    this.buffer = this.#buffer;
    this.#waitings = [];
  }

  public stream() {
    return new ReadableStream<T>({
      start: this.tee.bind(this),
    });
  }

  public static fromStream<T>(stream: ReadableStream<T>) {
    const lb = new this<T>();
    stream.pipeTo(
      new WritableStream({
        write: (item) => lb.enqueue(item),
        close: () => lb.close(),
        abort: (reason) => lb.error(reason),
      })
    );
    return lb;
  }

  public tee(controller: ReadingController<T>) {
    this.#buffer.forEach((item) => controller.enqueue?.(item));
    switch (this.state) {
      case BufferState.Open:
        this.#waitings.push(controller);
        break;
      case BufferState.Closed:
        controller.close?.();
        break;
      case BufferState.Error:
        controller.error?.(this.reason);
        break;
    }
  }

  public untee(controller: ReadingController<T>) {
    const index = this.#waitings.indexOf(controller);
    if (index === -1) return;
    this.#waitings.splice(index, 1);
  }

  public enqueue(item: T) {
    if (this.state !== BufferState.Open) throw new Error("Cannot call `enqueue` on a closed LiveBuffer");
    this.#buffer.push(item);
    this.#waitings.forEach((controller) => controller.enqueue?.(item));
  }

  public close() {
    if (this.state !== BufferState.Open) throw new Error("Cannot call `close` on a closed LiveBuffer");
    Object.defineProperty(this, "state", { value: BufferState.Closed, configurable: true, enumerable: true });
    this.#waitings.splice(0).forEach((controller) => controller.close?.());
  }

  public error(reason: unknown) {
    reason = fixReason(reason);
    if (this.state !== BufferState.Open) throw new Error("Cannot call `error` on a closed LiveBuffer");
    Object.defineProperty(this, "state", { value: BufferState.Error, configurable: true, enumerable: true });
    Object.defineProperty(this, "reason", { value: reason, configurable: true, enumerable: true });
    this.#waitings.splice(0).forEach((controller) => controller.error?.(reason));
  }

  public static closure<T>(slices: T[]) {
    const buf = new LiveBuffer<T>();
    slices.forEach((item) => buf.enqueue(item));
    buf.close();
    return buf satisfies IterableBuffer<T>;
  }

  private async promise() {
    switch (this.state) {
      case BufferState.Open:
        const stream = this.stream().getReader();
        for (;;) {
          const { done } = await stream.read();
          if (done) break;
        }
        return this.#buffer.slice(0) satisfies T[];
      case BufferState.Closed:
        return this.#buffer.slice(0) satisfies T[];
      case BufferState.Error:
        throw this.reason;
      default:
        throw new Error("Unreachable");
    }
  }

  public then<TResult1 = T[], TResult2 = never>(
    onfulfilled?: ((value: T[]) => TResult1 | PromiseLike<TResult1>) | null | undefined,
    onrejected?: ((reason: unknown) => TResult2 | PromiseLike<TResult2>) | null | undefined
  ): PromiseLike<TResult1 | TResult2> {
    return this.promise().then(onfulfilled, onrejected);
  }

  public [Symbol.asyncIterator](): AsyncIterator<T, unknown, unknown> {
    return this.stream()[Symbol.asyncIterator]();
  }

  public closeIfNeeded() {
    if (this.state === BufferState.Open) this.close();
  }

  public errorIfNeeded(reason: unknown) {
    if (this.state === BufferState.Open) this.error(reason);
  }
}

/**
 * Considering that some developers use `if (reason)` to check if the buffer is errored.
 * So we need to fix some special reason values to be an Error object.
 */
const fixReason = (reason: unknown): Error | object => {
  switch (typeof reason) {
    case "object":
    case "function":
      if (reason === null) return new Error(`LiveBuffer errored with null reason`);
      return reason;
    case "undefined":
      return new Error(`LiveBuffer errored with undefined reason`);
    case "bigint":
    case "boolean":
    case "number":
      return new Error(`LiveBuffer errored with ${typeof reason} reason ${reason}`);
    case "string":
      if (reason === "") return new Error(`LiveBuffer errored with empty string reason`);
      return new Error(reason);
    default:
      return new Error(`LiveBuffer errored with ${typeof reason} reason`);
  }
};
