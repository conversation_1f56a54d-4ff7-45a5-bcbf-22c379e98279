import { type ApiToolOptions, type JsonSchemaValue } from "../Chat";
import { type ChatBasic } from "../ChatBasic";
import { type ToolCallManagerOptions } from "./ToolCallManager";

const appendEntries = <T extends { append(key: string, value: string): void }>(
  target: T,
  entries: readonly (readonly [string, unknown | unknown[]])[]
) => {
  for (const [key, value] of entries) {
    if (value instanceof Array) {
      for (const v of value) target.append(key, String(v));
    } else if (value !== undefined) target.append(key, String(value));
  }
};

export const buildApiTool = (
  chat: ChatBasic<ToolCallManagerOptions>,
  options: ApiToolOptions,
  environmentVariables: Record<string, string>
) => {
  const { name, method, description } = options;

  const replaceEV = (input: string): string => {
    return input.replace(/\${(.*?)\}/g, (_, $1) => String(environmentVariables[$1]));
  };

  const parameters = {
    type: "object" as const,
    properties: {} as Record<string, JsonSchemaValue>,
    required: [] as string[],
  };

  if (options.pathVariables) {
    for (const [key, value] of Object.entries(options.pathVariables)) {
      if (value && typeof value === "object") {
        parameters.properties[key] = value;
        parameters.required.push(key);
      }
    }
  }

  if (options.headers) {
    if (options.headers?.properties && typeof options.headers?.properties === "object") {
      const requiredSet = new Set(options.headers?.required || []);
      for (const [key, value] of Object.entries(options.headers.properties)) {
        if (isJsonSchemaValue(value)) {
          parameters.properties[key] = value;
          if (requiredSet.has(key)) parameters.required.push(key);
        }
      }
    }
    // Legacy usage, has been deprecated, but still support for backward compatibility
    else {
      for (const [key, value] of Object.entries(options.headers)) {
        if (value && typeof value === "object") {
          parameters.properties[key] = value;
          parameters.required.push(key);
        }
      }
    }
  }

  if (options.query?.properties) {
    const requiredSet = new Set(options.query.required || []);
    for (const [key, value] of Object.entries(options.query.properties)) {
      if (isJsonSchemaValue(value)) {
        parameters.properties[key] = value;
        if (requiredSet.has(key)) parameters.required.push(key);
      }
    }
  }

  if (options.body?.properties) {
    const requiredSet = new Set(options.body.required || []);
    for (const [key, value] of Object.entries(options.body.properties)) {
      if (isJsonSchemaValue(value)) {
        parameters.properties[key] = value;
        if (requiredSet.has(key)) parameters.required.push(key);
      }
    }
  }
  const implementation = async (args: Record<string, unknown>) => {
    const headers = new Headers();

    if (options.headers) {
      if (options.headers?.properties && typeof options.headers?.properties === "object") {
        for (const [key, value] of Object.entries(options.headers.properties)) {
          if (isJsonSchemaValue(value)) {
            headers.set(key, String(args[key]));
          } else if (typeof value === "string") {
            headers.set(key, replaceEV(value));
          }
        }
      }
      // Legacy usage, has been deprecated, but still support for backward compatibility
      else {
        for (const [key, value] of Object.entries(options.headers)) {
          if (isJsonSchemaValue(value)) {
            headers.set(key, String(args[key]));
          } else if (typeof value === "string") {
            headers.set(key, replaceEV(value));
          }
        }
      }
    }

    if (!headers.has("Content-Type") && method.toUpperCase() !== "GET") {
      headers.set("Content-Type", "application/json");
    }

    const pathVariables: Record<string, string> = Object.create(null);
    if (options.pathVariables) {
      for (const [key, value] of Object.entries(options.pathVariables)) {
        if (isJsonSchemaValue(value)) {
          pathVariables[key] = String(args[key]);
        } else if (typeof value === "string") {
          pathVariables[key] = replaceEV(value);
        }
      }
    }

    const replacedUrl = options.url.replace(/\{(.*?)\}/g, (_, $1) => String(pathVariables[$1]));

    const url = new URL(replacedUrl);

    if (options.query?.properties) {
      for (const [key, value] of Object.entries(options.query.properties)) {
        if (isJsonSchemaValue(value)) {
          [args[key]].flat().map((v) => url.searchParams.append(key, String(v)));
        } else if (typeof value === "string" || isStringArray(value)) {
          [value].flat().map((v) => url.searchParams.append(key, replaceEV(v)));
        }
      }
    }

    const reqBodyEntries = Object.keys(options.body?.properties || {}).map((key) => [key, args[key]] as const);

    let body;
    if (method.toUpperCase() !== "GET" && method.toUpperCase() !== "HEAD") {
      const contentType = headers.get("Content-Type") || "";
      if (/^application\/x-www-form-urlencoded\b/i.test(contentType)) {
        body = new URLSearchParams();
        appendEntries(body, reqBodyEntries);
        body = body.toString();
      } else if (/^application\/multipart-formdata\b/i.test(contentType)) {
        body = new FormData();
        appendEntries(body, reqBodyEntries);
      } else {
        body = JSON.stringify(Object.fromEntries(reqBodyEntries));
      }
    }

    const response = await chat.fetch(url, { method, headers, body: body, credentials: "include" });
    try {
      const responseBody = await response.text();
      return {
        statusCode: response.status,
        errorBody: response.ok ? undefined : responseBody,
        result: response.ok ? parseResponseBody(responseBody) : undefined,
      };
    } catch (error) {
      return {
        statusCode: response.status,
        errorBody: error instanceof Error ? error.message : String(error),
        result: undefined,
      };
    }
  };

  const declaration = {
    name,
    description,
    parameters,
  };

  return { declaration, implementation };
};

const parseResponseBody = (responseBody: string) => {
  try {
    return JSON.parse(responseBody) as unknown;
  } catch (error) {
    return responseBody;
  }
};

const isJsonSchemaValue = (what: unknown): what is JsonSchemaValue =>
  typeof what === "object" && what !== null && "type" in what && typeof what.type === "string";

const isStringArray = (what: unknown): what is string[] =>
  what instanceof Array && what.every((item) => typeof item === "string");
