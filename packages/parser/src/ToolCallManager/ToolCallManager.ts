import type { ToolDeclarationFunction, ApiToolOptions, IframeToolOptions } from "../Chat";
import { type ChatBasic } from "../ChatBasic";
import { type IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { McpToolCallError } from "../errors/McpToolCallError";
import { ImplementedTool, type ImplResult } from "./ImplementedTool";
import { buildApiTool } from "./buildApiTool";
import { buildIframeTool, IframeToolResult } from "./buildIframeTool";
import { mcpCall } from "./mcpCall";

export interface ToolCallManagerOptions {
  /**
   * Register API tools.
   * NOTE: This is a extension property, it never be send to LLM.
   */
  readonly additional_api_tools?: readonly ApiToolOptions[];

  /**
   * Register MCP services.
   * NOTE: This is a extension property, it never be send to LLM.
   */
  readonly additional_mcp_servers?: readonly string[];

  /**
   * Select which native tools can be used.
   * NOTE: This is a extension property, it never be send to LLM.
   */
  readonly additional_native_tools?: readonly string[];

  /**
   * Register iframe tools.
   * NOTE: This is a extension property, it never be send to LLM.
   */
  readonly additional_iframe_tools?: readonly IframeToolOptions[];
}

export class ToolCallManager {
  readonly #chat: ChatBasic<ToolCallManagerOptions>;
  readonly #tools: ImplementedTool<string>[];
  readonly #allowedNativeTools: Set<string>;
  readonly #environmentVariables: Record<string, string>;
  readonly #allowedMcpServers: Set<string>;
  public readonly mcpServersInitializing: Promise<void> | null;

  constructor(chat: ChatBasic<ToolCallManagerOptions>, options: ToolCallManagerOptions) {
    this.#chat = chat;
    this.#tools = [];
    this.#allowedNativeTools = new Set();
    this.#allowedMcpServers = new Set();
    this.#environmentVariables = Object.create(null);

    if (options.additional_native_tools instanceof Array) {
      for (const tool of options.additional_native_tools) {
        if (typeof tool === "string") {
          this.#allowedNativeTools.add(tool);
        } else {
          console.warn("The `additional_native_tools` must be an array of strings, but got", tool);
        }
      }
    }

    if (options.additional_mcp_servers instanceof Array) {
      for (const service of options.additional_mcp_servers) {
        if (typeof service === "string") {
          this.#allowedMcpServers.add(service);
        } else {
          console.warn("The `additional_mcp_servers` must be an array of strings, but got", service);
        }
      }
    }

    if (options.additional_api_tools instanceof Array) {
      for (const tool of options.additional_api_tools) {
        if (typeof tool === "object" && tool !== null) {
          const { declaration, implementation } = buildApiTool(this.#chat, tool, this.#environmentVariables);
          this.registerTool(declaration, implementation);
        } else {
          console.warn("The `additional_api_tools` must be an array of objects, but got", tool);
        }
      }
    }

    if (options.additional_iframe_tools instanceof Array) {
      for (const tool of options.additional_iframe_tools) {
        if (typeof tool === "object" && tool !== null) {
          const { declaration, implementation } = buildIframeTool(this.#chat, tool, this.#environmentVariables);
          this.registerTool(declaration, implementation);
        } else {
          console.warn("The `additional_iframe_tools` must be an array of objects, but got", tool);
        }
      }
    }

    this.mcpServersInitializing = this.initMcpServers();
  }

  private initMcpServers() {
    if (this.#allowedMcpServers.size === 0) return null;
    return Promise.allSettled(
      Array.from(this.#allowedMcpServers, async (url) => {
        const buffer = await mcpCall(this.#chat, url, "tools/list", {});
        const tools = buffer.flatMap((result) => {
          const { tools } = Object(result);
          if (!tools || !(tools instanceof Array)) return [];
          return tools;
        });
        for (const tool of tools) {
          const { name, description, inputSchema } = Object(tool);
          if (typeof name !== "string") continue;
          if (typeof description !== "string" || description === undefined) continue;
          if (!inputSchema || typeof inputSchema !== "object") continue;
          this.registerTool({ name, description, parameters: inputSchema }, (args: unknown) => {
            const buffer = new LiveBuffer<string>();
            mcpCall(this.#chat, url, "tools/call", { name, arguments: args }).tee({
              enqueue: (result) => {
                const { content, isError } = Object(result);
                const textContent = [content]
                  .flat()
                  .map((i) => Object(i).text)
                  .filter((i) => i)
                  .join("");
                if (isError) {
                  buffer.errorIfNeeded(new McpToolCallError(textContent));
                } else {
                  buffer.enqueue(textContent);
                }
              },
              close: () => buffer.closeIfNeeded(),
              error: (reason) => buffer.errorIfNeeded(reason),
            });
            return buffer;
          });
        }
      })
    ).then((results) => {
      for (const result of results) {
        if (result.status === "rejected") {
          console.error("Failed to initialize MCP server", result.reason);
        }
      }
    });
  }

  public getToolDeclarations() {
    return this.#tools;
  }

  public findTool(name: string) {
    return this.#tools.find((t) => t.name === name);
  }

  public registerTool<T extends string>(
    declarationFunction: ToolDeclarationFunction<T>,
    implement: (args: Record<T, unknown>) => ImplResult
  ) {
    this.#tools.push(new ImplementedTool(declarationFunction, implement));
  }

  public registerNativeTool(
    declarationFunction: ToolDeclarationFunction,
    implement: (args: Record<string, unknown>) => Promise<unknown>
  ) {
    if (!this.#allowedNativeTools.has(declarationFunction.name)) return false;
    this.registerTool(declarationFunction, implement);
    return true;
  }

  public registerEnvironmentVariables(variables: Record<string, string>) {
    for (const [name, value] of Object.entries(variables)) {
      this.#environmentVariables[name] = value;
    }
  }

  public async getTool(name: IterableBuffer<string>) {
    const nameString = (await name).join("");
    const tool = this.findTool(nameString);
    if (!tool) throw new Error(`Tool '${nameString}' is not found`);
    return tool;
  }

  /**
   * NOTE: This method will never be rejected.
   */
  public invokeTool(
    func: {
      name: IterableBuffer<string>;
      arguments: IterableBuffer<string>;
    },
    abortController: AbortController | undefined,
    clientMessages: LiveBuffer<string>
  ) {
    const output = new LiveBuffer<string>();
    this.getTool(func.name)
      .then(async (tool) => {
        const jsonArgs = (await func.arguments).join("");
        const parsedArgs = JSON.parse(jsonArgs || "{}");

        const what = tool.implementation.call(null, parsedArgs);

        const aborted = new Promise<never>((_, reject) => {
          abortController?.signal.addEventListener("abort", ({ target }) => {
            if (target instanceof AbortSignal) return reject(target.reason);
            throw new Error("Never reach here: AbortSignal is not an instance of AbortSignal in abort event handler");
          });
        });

        if (what instanceof Promise) {
          const result = await Promise.race([what, aborted]);
          output.enqueue(JSON.stringify(result));
          output.close();
        } else if (what instanceof IframeToolResult) {
          clientMessages.enqueue(what.toString());
          const result = await Promise.race([what, aborted]);
          output.enqueue(JSON.stringify(result));
          output.close();
        } else {
          const iterator = what[Symbol.asyncIterator]();
          for (;;) {
            const v = await Promise.race([iterator.next(), aborted]);
            if (v.done) {
              output.close();
              break;
            }
            output.enqueue(v.value);
          }
        }
      })
      .catch((error) => {
        output.enqueue("<error>");
        if (error instanceof Error) {
          if (error.message) {
            output.enqueue(error.message);
          } else {
            output.enqueue(error.name);
          }
        } else {
          output.enqueue("Unknown Error");
        }
        output.enqueue("</error>");
        output.close();
      });

    return { ...func, result: output };
  }
}
