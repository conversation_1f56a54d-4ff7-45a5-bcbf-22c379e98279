import { type IframeToolOptions, type JsonSchemaValue } from "../Chat";
import { type ChatBasic } from "../ChatBasic";
import { type ToolCallManagerOptions } from "./ToolCallManager";

export class IframeToolResult implements PromiseLike<string> {
  readonly href: string;
  readonly #promise: Promise<string>;

  constructor(href: string) {
    this.#promise = new Promise<string>((resolve) => {
      const handler = (event: MessageEvent) => {
        const { jsonrpc, method, params } = Object(event.data) as Record<string, unknown>;
        if (jsonrpc !== "2.0") return;
        if (method === "iframe-call-finish") {
          const { href, content } = Object(params) as Record<string, unknown>;
          if (href === this.href) {
            resolve(typeof content === "string" ? content : JSON.stringify(content));
            window.removeEventListener("message", handler);
          }
        }
      };
      window.addEventListener("message", handler);
    });
    this.href = href;
  }

  public then<TResult1 = string, TResult2 = never>(
    onfulfilled?: ((value: string) => TResult1 | PromiseLike<TResult1>) | null | undefined,
    onrejected?: ((reason: unknown) => TResult2 | PromiseLike<TResult2>) | null | undefined
  ): PromiseLike<TResult1 | TResult2> {
    return this.#promise.then(onfulfilled, onrejected);
  }

  public toString() {
    return `<iframe src="${this.href}" frameborder="0" style="width: 100%; height: 300px; border-radius: 10px;" />`;
  }
}

export const buildIframeTool = (
  _: ChatBasic<ToolCallManagerOptions>,
  options: IframeToolOptions,
  environmentVariables: Record<string, string>
) => {
  const { name, description } = options;

  const replaceEV = (input: string): string => {
    return input.replace(/\${(.*?)\}/g, (_, $1) => String(environmentVariables[$1]));
  };

  const parameters = {
    type: "object" as const,
    properties: {} as Record<string, JsonSchemaValue>,
    required: [] as string[],
  };

  if (options.pathVariables) {
    for (const [key, value] of Object.entries(options.pathVariables)) {
      if (isJsonSchemaValue(value)) {
        parameters.properties[key] = value;
        parameters.required.push(key);
      }
    }
  }

  if (options.query?.properties) {
    const requiredSet = new Set(options.query.required || []);
    for (const [key, value] of Object.entries(options.query.properties)) {
      if (isJsonSchemaValue(value)) {
        parameters.properties[key] = value;
        if (requiredSet.has(key)) parameters.required.push(key);
      }
    }
  }

  const implementation = (args: Record<string, unknown>) => {
    const pathVariables: Record<string, string> = Object.create(null);
    if (options.pathVariables) {
      for (const [key, value] of Object.entries(options.pathVariables)) {
        if (isJsonSchemaValue(value)) {
          pathVariables[key] = String(args[key]);
        } else if (typeof value === "string") {
          pathVariables[key] = replaceEV(value);
        }
      }
    }

    const replacedUrl = options.url.replace(/\{(.*?)\}/g, (_, $1) => String(pathVariables[$1]));

    const url = new URL(replacedUrl);

    if (options.query?.properties) {
      for (const [key, value] of Object.entries(options.query.properties)) {
        if (isJsonSchemaValue(value)) {
          [args[key]].flat().map((v) => url.searchParams.append(key, String(v)));
        } else if (typeof value === "string" || isStringArray(value)) {
          [value].flat().map((v) => url.searchParams.append(key, replaceEV(v)));
        }
      }
    }

    return new IframeToolResult(url.toString());
  };

  const declaration = {
    name,
    description,
    parameters,
  };

  return { declaration, implementation };
};

const isJsonSchemaValue = (what: unknown): what is JsonSchemaValue =>
  typeof what === "object" && what !== null && "type" in what && typeof what.type === "string";

const isStringArray = (what: unknown): what is string[] =>
  what instanceof Array && what.every((item) => typeof item === "string");
