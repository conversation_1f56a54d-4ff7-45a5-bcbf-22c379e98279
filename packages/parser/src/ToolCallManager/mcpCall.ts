import type { ChatBasic } from "../ChatBasic";
import type { ToolCallManagerOptions } from "./ToolCallManager";
import { type IterableBuffer, LiveBuffer } from "../IterableBuffer";
import { JsonRpcError } from "../errors/JsonRpcError";
import { SseTransform } from "../SseTransform";

export const mcpCall = (
  chat: ChatBasic<ToolCallManagerOptions>,
  url: string,
  method: "tools/list" | "tools/call",
  params: unknown
): IterableBuffer<unknown> => {
  const buffer = new LiveBuffer<unknown>();
  Promise.resolve()
    .then(() =>
      chat.fetch(url, {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json", Accept: "event/stream, application/json" },
        body: JSON.stringify({ jsonrpc: "2.0", method, id: Date.now(), params }),
      })
    )
    .then(async (response) => {
      if (!response.ok) throw new Error(`Got status code ${response.status} from ${url}`);
      const { body } = response;
      if (!body) throw new Error(`Got empty body from ${url}`);
      const contentType = response.headers.get("Content-Type");
      if (contentType && /^application\/json\s*(;|$)/.test(contentType)) {
        buffer.enqueue(await response.json());
        buffer.closeIfNeeded();
      } else {
        await body
          .pipeThrough(new TextDecoderStream())
          .pipeThrough(new SseTransform())
          .pipeTo(
            new WritableStream({
              write: (event) => {
                if (event.event !== "message") return;
                const { data } = event;
                if (!data || typeof data !== "string" || data[0] !== "{") return;
                try {
                  buffer.enqueue(JSON.parse(data));
                } catch (e) {
                  buffer.errorIfNeeded(e);
                }
              },
              close: () => buffer.closeIfNeeded(),
              abort: (e) => buffer.errorIfNeeded(e),
            })
          );
      }
    })
    .catch((e) => {
      buffer.errorIfNeeded(e);
    });

  const output = new LiveBuffer<undefined>();
  buffer.tee({
    enqueue: (value) => {
      const { result, error } = Object(value);
      if (error) output.errorIfNeeded(new JsonRpcError(error));
      output.enqueue(result);
    },
    close() {
      output.closeIfNeeded();
    },
    error(e) {
      output.errorIfNeeded(e);
    },
  });
  return output;
};
