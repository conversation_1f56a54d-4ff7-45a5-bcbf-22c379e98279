import { type ToolDeclaration, type ToolDeclarationFunction } from "../Chat";
import { type IterableBuffer } from "../IterableBuffer";
import { type IframeToolResult } from "./buildIframeTool";

export type ImplResult = Promise<unknown> | IterableBuffer<string> | IframeToolResult;

export class ImplementedTool<T extends string> implements ToolDeclaration<T> {
  public readonly type: "function";
  public readonly implementation: (args: Record<T, unknown>) => ImplResult;
  public readonly function: ToolDeclaration<T>["function"];

  // The getter method will not be serialized.
  public get name() {
    return this.function.name;
  }

  constructor(
    declarationFunction: ToolDeclarationFunction<T>,
    implementation: (args: Record<T, unknown>) => ImplResult
  ) {
    this.type = "function";
    this.implementation = implementation;
    this.function = declarationFunction;
  }
}
