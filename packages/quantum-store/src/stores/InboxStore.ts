import { computeIfAbsent } from "../lib/computeIfAbsent";

export interface InboxMessage {
  readonly user: string;
  readonly timestamp: string;
  readonly commitHash: string;
}

export interface QuantumInboxMessageForText {
  readonly type: "plainText";
  readonly message: string;
}

export interface QuantumInboxMessageForRequestAgentPermission {
  readonly type: "requestAgentPermission";
  readonly agentId: string;
}

export interface QuantumInboxMessageForRequestSitePermission {
  readonly type: "requestSitePermission";
  readonly siteId: string;
}

export type QuantumInboxMessage =
  | QuantumInboxMessageForText
  | QuantumInboxMessageForRequestAgentPermission
  | QuantumInboxMessageForRequestSitePermission;

import { rejectIfError } from "../lib/rejectIfError";
import { TagStore, TagStoreType } from "./TagStore";

export class InboxStore extends TagStore<InboxMessage[]> {
  constructor(user: string) {
    super(TagStoreType.Inbox, `${user}/quantum-playground/notifications`);
  }

  public static getInboxCommit(commitHash: string) {
    return InboxStore.getJsonCommit<QuantumInboxMessage>(commitHash);
  }

  async remove(commitHashes: string[]) {
    if (commitHashes.length === 0) return;
    const usp = new URLSearchParams();
    for (const commitHash of commitHashes) {
      usp.append("commitHashes", commitHash);
    }
    await InboxStore.fetch(`${this.base}?action=delete`, {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: usp.toString(),
      credentials: "include",
    }).then(rejectIfError);
    this.getWithoutCache();
  }

  async send(message: QuantumInboxMessage) {
    const commitHash = await TagStore.createJsonCommit(message);
    await InboxStore.fetch(`${this.base}?action=send`, {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams({ commitHash }).toString(),
      credentials: "include",
    }).then(rejectIfError);
    this.getWithoutCache();
  }

  async update(commitHash: string, message: QuantumInboxMessage) {
    commitHash = await TagStore.createJsonCommit(message, commitHash);
    await InboxStore.fetch(`${this.base}?action=update`, {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams({ commitHash }).toString(),
      credentials: "include",
    }).then(rejectIfError);
    this.getWithoutCache();
  }
}

const inboxCache = new Map<string, InboxStore>();
export const getInboxStore = (user: string) => computeIfAbsent(inboxCache, user, () => new InboxStore(user));
