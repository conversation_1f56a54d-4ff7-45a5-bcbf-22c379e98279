import { computeIfAbsent } from "../lib/computeIfAbsent";
import type { QuantumAgentConfig } from "../quantumAgentConfig";
import { TagStore, TagStoreType } from "./TagStore";
import { TagResult } from "./TagResult";

export type FinalQuantumAgentConfig = Extract<QuantumAgentConfig, { aiType: string }>;

export const isFinalQuantumAgentConfig = (data: QuantumAgentConfig): data is FinalQuantumAgentConfig =>
  !("href" in data) || typeof data.href !== "string";

const finalConfigCache = new WeakMap<
  TagResult<QuantumAgentConfig, AgentStore>,
  Promise<TagResult<FinalQuantumAgentConfig, AgentStore>>
>();

const loadFinalConfigWithoutCache = async (lastResult: TagResult<QuantumAgentConfig, AgentStore>) => {
  if (!lastResult.data) return lastResult as TagResult<FinalQuantumAgentConfig, AgentStore>;
  if (isFinalQuantumAgentConfig(lastResult.data)) return lastResult as TagResult<FinalQuantumAgentConfig, AgentStore>;
  const res = await TagStore.fetch(lastResult.data.href, { credentials: "include" });
  const finalData = Object(await res.json());
  return Object.create(lastResult, {
    data: { value: finalData, configurable: true, enumerable: true },
  }) as TagResult<FinalQuantumAgentConfig, AgentStore>;
};

export class AgentStore extends TagStore<QuantumAgentConfig> {
  public readonly id: string;
  constructor(agentId: string) {
    super(TagStoreType.Protected, `quantum-playground/agents/${agentId}.json`);
    this.id = agentId;
  }

  public async getFinalConfig(): Promise<TagResult<FinalQuantumAgentConfig, AgentStore>> {
    const input = await super.get();
    return computeIfAbsent(finalConfigCache, input, () => {
      const res = loadFinalConfigWithoutCache(input);
      res.catch((error) => {
        finalConfigCache.delete(input);
        throw error;
      });
      return res;
    });
  }
}

const agentCache = new Map<string, AgentStore>();
export const getAgentStore = (agentId: string) => {
  return computeIfAbsent(agentCache, agentId, () => new AgentStore(agentId));
};
