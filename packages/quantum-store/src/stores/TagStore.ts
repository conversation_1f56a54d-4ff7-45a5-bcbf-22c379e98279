import { rejectIfError } from "../lib/rejectIfError";
import { computeIfAbsent } from "../lib/computeIfAbsent";
import { TagResult } from "./TagResult";

export enum TagStoreType {
  Public = "public-tags",
  Protected = "protected-tags",
  Private = "private-tags",
  Inbox = "inbox-tags",
}

export interface Commit {
  readonly parent?: string;
  readonly objectHash: string;
  readonly contentType?: string;
  readonly owner: string;
  readonly meta?: Record<string, string>;
  readonly timestamp: string;
}

const commitRawCache = new Map<string, Promise<Commit>>();
const commitDataCache = new Map<string, Promise<ArrayBuffer>>();

export class TagStore<T> {
  public static base = "https://fe-tools.huolala.work";
  public static fetch = fetch.bind(window);
  public lastResult: TagResult<T, this> | undefined;
  public type: TagStoreType;
  public tagName: string;
  public eventBus: EventTarget;
  private busy?: Promise<TagResult<T, this>>;

  public static async createJsonCommit(content: unknown, parent?: string) {
    const usp = new URLSearchParams({ action: "upload" });
    if (parent) usp.set("parent", parent);
    return fetch(`${TagStore.base}/commits?${usp.toString()}`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(content),
      credentials: "include",
    })
      .then(rejectIfError)
      .then(async (res) => res.text());
  }

  public static async getJsonCommit<T>(commitHash: string) {
    return computeIfAbsent(commitDataCache, commitHash, () => {
      return fetch(`${TagStore.base}/commits/${commitHash}`, { method: "GET", credentials: "include" })
        .then(rejectIfError)
        .then((res) => res.arrayBuffer());
    }).then((data) => JSON.parse(new TextDecoder().decode(data)) as T);
  }

  public static async getCommitRaw(commitHash: string) {
    return computeIfAbsent(commitRawCache, commitHash, () => {
      return fetch(`${TagStore.base}/commits/${commitHash}?info=commit`, { method: "GET", credentials: "include" })
        .then(rejectIfError)
        .then((res) => res.json());
    });
  }

  public get base() {
    return `${TagStore.base}/${this.type}/${this.tagName}`;
  }

  public get head() {
    return this.lastResult?.head ?? "null";
  }

  constructor(type: TagStoreType, tagName: string) {
    this.type = type;
    this.tagName = tagName;
    this.eventBus = new EventTarget();
  }

  public async get() {
    return this.lastResult || this.getWithoutCache();
  }

  public getWithoutCache() {
    if (this.busy) return this.busy;
    const headers: Record<string, string> = { "X-Null-If-Not-Found": "true" };
    if (this.lastResult?.head) headers["If-None-Match"] = this.lastResult.head;
    this.busy = TagStore.fetch(this.base, {
      method: "GET",
      headers,
      credentials: "include",
    })
      .then(rejectIfError)
      .then(async (res: Response) => {
        if (res.status === 304) {
          if (!this.lastResult) throw new Error("Got 304 but no last result");
          return this.lastResult;
        }
        return new TagResult<T, this>(this, res.headers, await res.json());
      })
      .then((result) => {
        if (result !== this.lastResult) {
          this.lastResult = result;
          this.eventBus.dispatchEvent(new CustomEvent("update", { detail: result }));
        }
        delete this.busy;
        return result;
      })
      .catch((error) => {
        this.eventBus.dispatchEvent(new CustomEvent("error", { detail: error }));
        delete this.busy;
        throw error;
      });
    return this.busy;
  }

  public async getHead() {
    return TagStore.fetch(`${this.base}?info=head`, { method: "GET", credentials: "include" })
      .then(rejectIfError)
      .then(async (res) => res.text());
  }

  public async upload(data: T, ifMatchParent: string) {
    await TagStore.fetch(`${this.base}?action=upload`, {
      method: "POST",
      headers: { "If-Match-Parent": ifMatchParent, "Content-Type": "application/json" },
      body: JSON.stringify(data),
      credentials: "include",
    }).then(rejectIfError);
    this.getWithoutCache();
  }

  public async patch(operations: { path: string; op: string; value?: unknown }[], ifMatchParent: string) {
    await TagStore.fetch(this.base, {
      method: "PATCH",
      body: JSON.stringify(operations),
      headers: { "If-Match-Parent": ifMatchParent, "Content-Type": "application/json" },
      credentials: "include",
    }).then(rejectIfError);
    this.getWithoutCache();
  }

  public async delete(ifMatchParent: string) {
    if (this.type === TagStoreType.Public) {
      throw new Error("You cannot delete a public tag");
    }
    await TagStore.fetch(this.base, {
      method: "DELETE",
      headers: { "If-Match-Parent": ifMatchParent },
      credentials: "include",
    }).then(rejectIfError);
    this.getWithoutCache();
  }

  public async modifyAuthorizedUsers(modify: (authorizedUsers: Set<string>) => unknown | Promise<unknown>) {
    if (this.type !== TagStoreType.Protected) {
      throw new Error("The `modifyAuthorizedUsers` method is only supported for protected tags");
    }
    return this.transaction(async (tr) => {
      const authorizedUsers = new Set(tr.getAuthorizedUsers());
      await modify(authorizedUsers);
      if (!tr.head) throw new Error("The tag is not initialized yet, cannot update authorized users");
      await this.updateAuthorizedUsers(Array.from(authorizedUsers), tr.head);
    });
  }

  private async updateAuthorizedUsers(authorizedUsers: string[], ifMatchParent: string) {
    if (this.type !== TagStoreType.Protected) {
      throw new Error("The `updateAuthorizedUsers` method is only supported for protected tags");
    }
    await TagStore.fetch(`${this.base}?action=updateAuthorizedUsers`, {
      method: "POST",
      headers: { "Content-Type": "application/json", "If-Match-Parent": ifMatchParent },
      body: JSON.stringify(authorizedUsers),
      credentials: "include",
    }).then(rejectIfError);
    this.getWithoutCache();
  }

  public async transaction<R>(handler: (result: TagResult<T, this>) => Promise<R>, retries = 3): Promise<R> {
    for (;;) {
      try {
        return await handler(await (this.busy || this.get()));
      } catch (error) {
        if (retries-- > 0 && Object(error).name === "ConsistencyException") {
          this.getWithoutCache();
          continue;
        }
        throw error;
      }
    }
  }
}

export class ListTagStore<T> extends TagStore<T[]> {
  constructor(type: TagStoreType, tagName: string) {
    super(type, tagName);
  }

  async add(item: T) {
    await this.transaction(async ({ head }) => {
      if (!head) {
        await this.upload([item], "null");
      } else {
        await this.patch([{ path: `/-`, op: "add", value: item }], head);
      }
    });
  }

  async remove(item: T) {
    await this.transaction(async ({ data, head }) => {
      if (!head || !data) return;
      const index = data.indexOf(item);
      if (index === -1) return;
      await this.patch([{ path: `/${index}`, op: "remove" }], head);
    });
  }
}
