export class TagResult<T, S> {
  public readonly store: S;
  public readonly parent: string | null;
  public readonly head: string | null;
  public readonly owner: string;
  public readonly timestamp: Date | null;
  public readonly meta: Record<string, string>;
  public readonly data: T | null;

  public getAuthorizedUsers() {
    const authorizedUsers = this.meta.authorizedUsers;
    if (typeof authorizedUsers !== "string") return [];
    return authorizedUsers
      .split(",")
      .map((user) => user.trim())
      .filter((i) => i);
  }

  constructor(store: S, headers: Headers, body: T) {
    this.store = store;
    this.parent = headers.get("X-Commit-Parent");
    this.head = headers.get("X-Commit-Hash");
    this.owner = headers.get("X-Commit-Owner") ?? "unknown";
    const isoTimestamp = headers.get("X-Commit-Timestamp");
    this.timestamp = isoTimestamp ? new Date(isoTimestamp) : null;
    this.meta = JSON.parse(headers.get("X-Commit-Meta") ?? "{}") as Record<string, string>;
    this.data = body;
  }
}

export type TagResultNx<T, S> = TagResult<T, S> & { data: T };
