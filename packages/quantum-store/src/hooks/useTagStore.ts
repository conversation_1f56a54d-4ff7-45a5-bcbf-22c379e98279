import { useEventListener } from "@hll/use-event-listener";
import { useState, useEffect } from "react";
import { TagResult } from "../stores/TagResult";
import { TagStore } from "../stores/TagStore";

interface TagStoreState<T, S extends TagStore<T>> {
  result?: TagResult<T, S>;
  error?: unknown;
  loading: boolean;
}

export const useTagStore = <S extends TagStore<T>, T>(store: (S & TagStore<T>) | undefined) => {
  const [state, setState] = useState<TagStoreState<T, S>>({
    result: store?.lastResult,
    loading: store ? store.lastResult === undefined : false,
  });

  useEventListener(
    "update",
    () => {
      if (!store) return;
      if (!store.lastResult) return;
      setState((prev) => ({ ...prev, result: store.lastResult }));
    },
    { target: store?.eventBus }
  );

  useEffect(() => {
    if (!store) return;
    setState((prev) => ({ ...prev, loading: true }));
    store
      .get()
      .then((res) => {
        setState({
          result: res,
          loading: false,
        });
      })
      .catch((error) => {
        setState({
          error,
          loading: false,
        });
      });
  }, [store]);
  const { result, error, loading } = state;

  return {
    error,
    loading,
    result,
    get head() {
      return result?.head;
    },
    get owner() {
      return result?.owner;
    },
    get timestamp() {
      return result?.timestamp;
    },
    get data() {
      return result?.data;
    },
    get store() {
      return result?.store;
    },
  };
};
