import { useState, useEffect } from "react";
import { FinalQuantumAgentConfig, getAgentStore } from "../stores/AgentStore";
import { AgentStore } from "../stores/AgentStore";
import { TagResultNx } from "../stores/TagResult";
import { TagStore } from "../stores/TagStore";
import { useTagStore } from "./useTagStore";

export const getAllFinalConfigs = async (stores: AgentStore[]) =>
  Promise.all(
    stores.map(async (store) => {
      try {
        const tr = await store.getFinalConfig();
        if (!tr.data) return null;
        return tr as TagResultNx<FinalQuantumAgentConfig, AgentStore>;
      } catch (error) {
        console.error(`Failed to get final config by ID ${store.id}:`, error);
        return null;
      }
    })
  ).then((trs) =>
    trs.filter((c) => c !== null).sort((a, b) => (b.timestamp?.getTime() ?? 0) - (a.timestamp?.getTime() ?? 0))
  );

export const useAllAgentConfigs = (store: TagStore<string[]> | undefined) => {
  const { data: ids, loading, error } = useTagStore(store);

  const [state, setState] = useState<{
    data?: TagResultNx<FinalQuantumAgentConfig, AgentStore>[];
    error?: unknown;
    loading: boolean;
  }>({ loading: true });

  useEffect(() => {
    if (loading || error) return;
    if (!ids || ids.length === 0) {
      setState({ data: [], loading: false });
      return;
    }
    const stores = ids.map((id) => getAgentStore(id));

    // The `getAllFinalConfigs` method may trigger 'update' event,
    // so we need to prevent the update from being called multiple times.
    let inc = 0;
    const update = async () => {
      if (inc++ > 0) return;
      setState((prev) => ({ ...prev, loading: true }));
      try {
        const data = await getAllFinalConfigs(stores);
        setState({ data, loading: false });
      } catch (error) {
        setState((prev) => ({ ...prev, error, loading: false }));
      } finally {
        inc = 0;
      }
    };

    update();

    // Watch for updates to the agent configs
    stores.forEach((store) => store.eventBus.addEventListener("update", update));
    return () => stores.forEach((store) => store.eventBus.removeEventListener("update", update));
  }, [ids, loading, error]);

  return { ...state, loading: state.loading || loading, error: state.error || error };
};
