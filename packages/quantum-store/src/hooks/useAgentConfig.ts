import { useState, useEffect } from "react";
import { getAgentStore } from "../stores/AgentStore";
import { TagResult } from "../stores/TagResult";
import { AgentStore, FinalQuantumAgentConfig } from "../stores/AgentStore";

export const useAgentConfig = (agent: string) => {
  const store = getAgentStore(agent);

  const [state, updateState] = useState<{
    result: TagResult<FinalQuantumAgentConfig, AgentStore> | null;
    error: unknown;
    loading: boolean;
  }>({
    result: null,
    error: null,
    loading: true,
  });

  useEffect(() => {
    let unmounted = false;
    updateState((prev) => ({ ...prev, loading: true }));
    store.getFinalConfig().then(
      (data) => {
        if (unmounted) return;
        updateState({ result: data, error: null, loading: false });
      },
      (error) => {
        if (unmounted) return;
        updateState({ result: null, error, loading: false });
      }
    );
    return () => {
      unmounted = true;
    };
  }, [store.lastResult]);

  const { result, ...rest } = state;
  return { result, ...result, ...rest };
};
