import { useState, useEffect } from "react";
import { getSiteStore, type SiteConfig, type SiteStore } from "../stores/SiteStore";
import { TagStore } from "../stores/TagStore";
import { TagResultNx } from "../stores/TagResult";
import { useTagStore } from "./useTagStore";

export const getAllSiteConfig = async (stores: SiteStore[]) =>
  Promise.all(
    stores.map(async (store) => {
      try {
        const tr = await store.get();
        if (!tr.data) return null;
        if (!store.id) throw new Error(`Invalid site ID`);
        return tr as TagResultNx<SiteConfig, SiteStore>;
      } catch (error) {
        console.error(`Failed to get site config by ID ${store.id}:`, error);
        return null;
      }
    })
  ).then((configs) =>
    configs.filter((c) => c !== null).sort((a, b) => (b.timestamp?.getTime() ?? 0) - (a.timestamp?.getTime() ?? 0))
  );

export const useAllSiteConfigs = (store: TagStore<string[]> | undefined) => {
  const { data: ids, loading, error } = useTagStore(store);

  const [state, setState] = useState<{
    data?: TagResultNx<SiteConfig, SiteStore>[];
    error?: unknown;
    loading: boolean;
  }>({ loading: true });

  useEffect(() => {
    if (loading || error) return;
    if (!ids || ids.length === 0) {
      setState({ data: [], loading: false });
      return;
    }

    const stores = ids.map((id) => getSiteStore(id));

    // The `getAllSiteConfig` method may trigger 'update' event,
    // so we need to prevent the update from being called multiple times.
    let inc = 0;
    const update = async () => {
      if (inc++ > 0) return;
      setState((prev) => ({ ...prev, loading: true }));
      try {
        const data = await getAllSiteConfig(stores);
        setState({ data, loading: false });
      } catch (error) {
        setState((prev) => ({ ...prev, error, loading: false }));
      } finally {
        inc = 0;
      }
    };

    update();

    // Watch for updates to the agent configs
    stores.forEach((store) => store.eventBus.addEventListener("update", update));
    return () => stores.forEach((store) => store.eventBus.removeEventListener("update", update));
  }, [ids, loading, error]);

  return { ...state, loading: state.loading || loading, error: state.error || error };
};
