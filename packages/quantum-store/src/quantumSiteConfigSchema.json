{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Quantum Box Site Config Schema", "description": "Configuration for Quantum Box Agent", "type": "object", "properties": {"$schema": {"type": "string", "format": "uri"}, "url": {"type": "string", "description": "The url of the site"}, "title": {"type": "string", "description": "The title of the site"}, "description": {"type": "string", "description": "The description of the site"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "The tags of the site"}, "icon": {"type": "string", "description": "The icon of the site"}}, "required": ["url", "title"], "additionalProperties": false}