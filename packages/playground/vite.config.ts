import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import vitePluginVanStatic from "@hll/vite-plugin-van";
import { viteStaticCopy } from "vite-plugin-static-copy";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    vitePluginVanStatic(),
    viteStaticCopy({
      targets: [
        {
          src: "../../node_modules/monaco-editor/min/*",
          dest: "monaco-editor",
        },
      ],
    }),
  ],
});
