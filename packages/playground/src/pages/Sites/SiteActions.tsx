import { useEffect, useMemo } from "react";
import { Button } from "../../components/Button";
import { Tip } from "../../components/Tip";
import { SaveIcon } from "../../icons/SaveIcon";
import { getInboxStore, getSiteStore, publicSitesStore, useTagStore } from "@hll/quantum-store";
import { useNavigate } from "react-router-dom";
import { showSuccess, showError } from "../../components/Notification";
import { useEditable } from "../../utils/useEditable";
import { PlusIcon } from "../../icons/PlusIcon";
import { SettingsIcon } from "../../icons/SettingsIcon";
import { PublicOrPrivateButton } from "../../components/PublicOrPrivateButton";
import { Tooltip } from "../../components/Tooltip";

interface SiteActionsProps {
  siteId: string;
  jsonValue: string;
}

const handleSave = async (siteId: string, jsonValue: string) => {
  if (!siteId) return;
  const store = getSiteStore(siteId);
  try {
    await store.upload(JSON.parse(jsonValue), store.head);
    showSuccess("保存成功");
  } catch (e) {
    showError(e, "保存失败");
  }
};

const SiteActions = ({ siteId, jsonValue }: SiteActionsProps) => {
  const navigate = useNavigate();
  const store = getSiteStore(siteId);
  const { data: siteData, loading: siteDataLoading, error: siteDataError, result } = useTagStore(store);
  const { data: editable, loading: editableLoading, error: editableError } = useEditable(result);

  const loading = siteDataLoading || editableLoading;
  const error = siteDataError || editableError;

  const hasChanges = useMemo(() => {
    if (!siteData) return false;
    try {
      return JSON.stringify(siteData, null, 2) !== jsonValue;
    } catch {
      return false;
    }
  }, [siteData, jsonValue]);

  useEffect(() => {
    if (error) {
      showError(error);
    }
  }, [error]);

  useEffect(() => {
    if (!hasChanges) return;
    const handler = (e: BeforeUnloadEvent) => e.preventDefault();
    window.addEventListener("beforeunload", handler);
    return () => window.removeEventListener("beforeunload", handler);
  }, [hasChanges]);

  if (loading) {
    return <Tip type="loading" />;
  }

  if (editable !== true) {
    if (result?.owner)
      return (
        <Button
          icon={<PlusIcon />}
          onClick={async () => {
            const inbox = getInboxStore(result.owner);
            try {
              await inbox.send({ type: "requestSitePermission", siteId });
              showSuccess("申请已发送成功");
            } catch (e) {
              showError(e, "申请失败");
            }
          }}
        >
          申请权限
        </Button>
      );
    return null;
  }

  return (
    <>
      {hasChanges ? null : <Tip type="weak">当前无变更</Tip>}
      <Button
        variant={hasChanges ? "primary" : "default"}
        onClick={() => handleSave(siteId, jsonValue)}
        icon={<SaveIcon />}
      >
        保存
      </Button>
      <PublicOrPrivateButton id={siteId} listTagStore={publicSitesStore} />
      <Tooltip content="设置" placement="bottom">
        <Button onClick={() => navigate(`/sites/${siteId}/settings`)} icon={<SettingsIcon />} />
      </Tooltip>
    </>
  );
};

export default SiteActions;
