import { createContext, useContext, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import Editor, { loader, type OnMount } from "@monaco-editor/react";
import { Tip } from "../../components/Tip";
import { getSiteStore, useTagStore } from "@hll/quantum-store";
import { useSlot } from "@hll/use-slot";
import SiteActions from "./SiteActions";

import "./SiteEditor.css";

loader.config({
  paths: {
    vs: location.origin + "/monaco-editor/vs",
  },
});

const handleEditorMount: OnMount = (_, monaco) => {
  monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
    validate: true,
    enableSchemaRequest: true,
  });
};

const SiteIdContext = createContext<string>("");

const SiteIdProvider = ({ children }: { children: React.ReactNode }) => {
  const { siteId = "" } = useParams();
  return <SiteIdContext.Provider value={siteId}>{children}</SiteIdContext.Provider>;
};

const EditorContainer = () => {
  const siteId = useContext(SiteIdContext);
  const [jsonValue, setJsonValue] = useState("");
  const { data, error, loading } = useTagStore(getSiteStore(siteId));

  useEffect(() => {
    if (data) {
      setJsonValue(JSON.stringify(data, null, 2));
    }
  }, [data, setJsonValue]);

  useSlot("MainHeader-left", () => <span className="site-id">ID: {siteId}</span>, [siteId]);

  useSlot("MainHeader-right", () => <SiteActions siteId={siteId} jsonValue={jsonValue} />, [siteId, jsonValue]);

  if (!jsonValue && loading)
    return (
      <Tip type="loading" className="select-site-tip">
        加载中...
      </Tip>
    );
  if (error)
    return (
      <Tip type="error" className="select-site-tip">
        加载失败: {String(error)}
      </Tip>
    );

  return (
    <div className={`EditorContainer${loading ? " loading" : ""}`}>
      <Editor
        loading={loading}
        defaultLanguage="json"
        value={jsonValue}
        onMount={handleEditorMount}
        onChange={(value) => setJsonValue(value || "")}
        options={{
          minimap: { enabled: false },
          fontSize: 14,
          lineNumbers: "on",
          roundedSelection: false,
          scrollBeyondLastLine: false,
          automaticLayout: true,
          theme: "vs-light",
        }}
      />
    </div>
  );
};

export default function SiteEditor() {
  useSlot(
    "MainHeader-title",
    () => {
      return "网址导航编辑";
    },
    []
  );
  return (
    <div className="SiteEditor">
      <SiteIdProvider>
        <EditorContainer />
      </SiteIdProvider>
    </div>
  );
}
