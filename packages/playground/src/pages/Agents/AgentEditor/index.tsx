import { createContext, useContext, useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import Editor, { loader, type OnMount } from "@monaco-editor/react";
import { Tip } from "../../../components/Tip";
import { verifyId } from "../../../services/agentIdManager";
import { getAgentStore, useTagStore } from "@hll/quantum-store";
import { useSlot } from "@hll/use-slot";
import { Button } from "../../../components/Button";
import { AgentActions } from "./AgentActions";

import "./index.css";
import { ChatIcon } from "../../../icons/ChatIcon";

loader.config({
  paths: {
    vs: location.origin + "/monaco-editor/vs",
  },
});

const handleEditorMount: OnMount = (_, monaco) => {
  monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
    validate: true,
    enableSchemaRequest: true,
  });
};

export const EditorContainer = () => {
  const agentId = useContext(AgentIdContext);
  const [jsonValue, setJsonValue] = useState("");
  const manager = useMemo(() => getAgentStore(agentId), [agentId]);

  const { data, error, loading } = useTagStore(manager);

  useEffect(() => {
    if (data) {
      setJsonValue(JSON.stringify(data, null, 2));
    }
  }, [data, setJsonValue]);

  useSlot(
    "MainHeader-left",
    () => {
      return (
        <>
          <span
            style={{
              color: "#64748b",
              fontSize: "0.875rem",
              background: "#e2e8f0",
              padding: "0px 1ch",
              borderRadius: "0.5ch",
            }}
          >
            ID: {agentId}
          </span>
          <div style={{ display: "flex", alignItems: "center", gap: "var(--gap)" }}>
            <Button
              variant="primary"
              icon={<ChatIcon />}
              onClick={() => {
                const agent = URL.createObjectURL(new Blob([jsonValue], { type: "application/json" }));
                window.open(`/chat?agent=${encodeURIComponent(agent)}`, "_blank");
              }}
            >
              开始对话
            </Button>
            <Tip type="weak">← 使用当前配置，无需保存</Tip>
          </div>
        </>
      );
    },
    [agentId, jsonValue]
  );

  useSlot(
    "MainHeader-right",
    () => {
      return <AgentActions agentId={agentId} jsonValue={jsonValue} />;
    },
    [agentId, jsonValue]
  );

  if (!jsonValue && loading)
    return (
      <Tip type="loading" className="select-agent-tip">
        加载中...
      </Tip>
    );
  if (error)
    return (
      <Tip type="error" className="select-agent-tip">
        加载失败: {Object(error).message}
      </Tip>
    );

  return (
    <div className={`EditorContainer ${loading ? "loading" : ""}`}>
      <Editor
        loading={loading}
        defaultLanguage="json"
        value={jsonValue}
        onMount={handleEditorMount}
        onChange={(value) => setJsonValue(value || "")}
        options={{
          minimap: { enabled: false },
          fontSize: 14,
          lineNumbers: "on",
          roundedSelection: false,
          scrollBeyondLastLine: false,
          automaticLayout: true,
          theme: "vs-light",
        }}
      />
    </div>
  );
};

const AgentIdContext = createContext<string>("");

const AgentIdProvider = ({ children }: { children: React.ReactNode }) => {
  const { agentId = "" } = useParams();

  if (agentId && !verifyId(agentId)) {
    return <Tip type="error">Illegal Agent ID</Tip>;
  }

  return <AgentIdContext.Provider value={agentId}>{children}</AgentIdContext.Provider>;
};

export const AgentEditor = () => {
  useSlot(
    "MainHeader-title",
    () => {
      return "智能体编辑";
    },
    []
  );

  return (
    <div className="AgentEditor">
      <AgentIdProvider>
        <EditorContainer />
      </AgentIdProvider>
    </div>
  );
};
