import { useEffect, useMemo } from "react";
import { Button } from "../../../components/Button";
import { Tip } from "../../../components/Tip";
import { SaveIcon } from "../../../icons/SaveIcon";
import { getAgentStore, getInboxStore, publicAgentsStore, useTagStore } from "@hll/quantum-store";
import { useNavigate } from "react-router-dom";
import { showSuccess, showError } from "../../../components/Notification";
import { PlusIcon } from "../../../icons/PlusIcon";
import { useEditable } from "../../../utils/useEditable";
import { SettingsIcon } from "../../../icons/SettingsIcon";
import { PublicOrPrivateButton } from "../../../components/PublicOrPrivateButton";
import { Tooltip } from "../../../components/Tooltip";

interface AgentActionsProps {
  agentId: string;
  jsonValue: string;
}

const handleSave = async (agentId: string, jsonValue: string) => {
  if (!agentId) return;
  const store = getAgentStore(agentId);
  try {
    await store.upload(JSON.parse(jsonValue), store.head);
    showSuccess("保存成功");
  } catch (e) {
    showError(e, "保存失败");
  }
};

const AgentActions = ({ agentId, jsonValue }: AgentActionsProps) => {
  const navigate = useNavigate();
  const store = getAgentStore(agentId);
  const { data: agentData, loading: agentDataLoading, error: agentDataError, result } = useTagStore(store);
  const { data: editable, loading: editableLoading, error: editableError } = useEditable(result);

  const loading = agentDataLoading || editableLoading;
  const error = agentDataError || editableError;

  const hasChanges = useMemo(() => {
    if (!agentData) return false;
    try {
      return JSON.stringify(agentData, null, 2) !== jsonValue;
    } catch {
      return false;
    }
  }, [agentData, jsonValue]);

  useEffect(() => {
    if (error) {
      showError(error);
    }
  }, [error]);

  useEffect(() => {
    if (!hasChanges) return;
    const handler = (e: BeforeUnloadEvent) => e.preventDefault();
    window.addEventListener("beforeunload", handler);
    return () => window.removeEventListener("beforeunload", handler);
  }, [hasChanges]);

  if (loading) {
    return <Tip type="loading" />;
  }

  if (editable) {
    return (
      <>
        {hasChanges ? null : <Tip type="weak">当前无变更</Tip>}
        <Button
          variant={hasChanges ? "primary" : "default"}
          onClick={() => handleSave(agentId, jsonValue)}
          icon={<SaveIcon />}
        >
          保存
        </Button>
        <PublicOrPrivateButton id={agentId} listTagStore={publicAgentsStore} />
        <Tooltip content="设置" placement="bottom">
          <Button onClick={() => navigate(`/agents/${agentId}/settings`)} icon={<SettingsIcon />} />
        </Tooltip>
      </>
    );
  } else {
    return (
      <>
        {result?.owner && (
          <Button
            icon={<PlusIcon />}
            onClick={async () => {
              const inbox = getInboxStore(result.owner);
              try {
                await inbox.send({ type: "requestAgentPermission", agentId });
                showSuccess("申请已发送成功");
              } catch (e) {
                showError(e, "申请失败");
              }
            }}
          >
            申请权限
          </Button>
        )}
      </>
    );
  }
};

export { AgentActions };
