import { useCallback, useRef, useState } from "react";
import { Dialog } from "../../components/Dialog";
import { showError, showSuccess } from "../../components/Notification";
import { searchUser, useUserInfo } from "../../services/user";
import { standardDebounce } from "../../utils/debounce";
import { Select } from "../../components/Select";
import { TagStore, useTagStore } from "@hll/quantum-store";
import { Button } from "../../components/Button";
import { PersonIcon } from "../../icons/PersonIcon";

const AddUserDialog = ({
  open,
  setOpen,
  store,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  store: TagStore<unknown>;
}) => {
  const [searchValue, setSearchValue] = useState("");
  const [searchUserList, setSearchUserList] = useState<{ label: string; value: string }[]>([]);
  const [selectLoading, setSelectLoading] = useState(false);
  const { data: userInfo } = useUserInfo();
  const { owner } = useTagStore(store);
  const updateMembers = async () => {
    if (!searchValue) {
      showError("请选择要添加的成员");
      return;
    }
    if (owner !== userInfo?.uniqId) {
      return showError("当前无权限操作");
    }
    try {
      await store.modifyAuthorizedUsers((authorizedUsers) => authorizedUsers.add(searchValue));
      showSuccess("添加成功");
      setSearchValue("");
      setSearchUserList([]);
      setOpen(false);
    } catch (e) {
      showError(e, "添加失败");
      return;
    }
  };

  const cnt = useRef(0);
  const searchUserApi = (keyword: string) => {
    setSelectLoading(true);
    const currentCnt = ++cnt.current;
    searchUser(keyword)
      .then((userList) => {
        const list = userList.map((u) => ({
          label: `${u.userName}(${u.userUniqId})`,
          value: u.userUniqId,
        }));
        if (currentCnt === cnt.current) {
          setSearchUserList(list);
        }
      })
      .finally(() => {
        if (currentCnt === cnt.current) {
          setSelectLoading(false);
        }
      });
  };

  const debouncedSearch = useCallback(
    standardDebounce(searchUserApi, 500),
    []
  );

  return (
    <Dialog
      width={450}
      mask={false}
      open={open}
      onClose={() => setOpen(false)}
      title="添加成员"
      onOk={updateMembers}
      onCancel={() => setOpen(false)}
      okText="确定"
      cancelText="取消"
    >
      <div style={{ display: "flex", alignItems: "center", gap: 8, marginLeft: 10 }}>
        <label style={{ minWidth: "40px", fontSize: "14px" }}>用户：</label>
        <Select
          placeholder="搜索并选择一个用户"
          showSearch
          onSearch={debouncedSearch}
          options={searchUserList}
          onChange={(value) => {
            setSearchValue(value as string);
          }}
          value={searchValue}
          loading={selectLoading}
        />
      </div>
    </Dialog>
  );
};

export const AddUserButton = ({ store }: { store: TagStore<unknown> }) => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Button icon={<PersonIcon />} variant="icon" onClick={() => setOpen(true)}></Button>
      <AddUserDialog open={open} setOpen={setOpen} store={store} />
    </>
  );
};
