import { AgentStore, SiteStore, useAgentConfig, useTagStore } from "@hll/quantum-store";
import { Tip } from "../../components/Tip";
import { useMemo } from "react";
import { neverReachHere } from "../../utils/neverReachHere";
import { getErrorMessage } from "../../utils/getErrorMessage";
import { useSlot } from "@hll/use-slot";
import { FeishuAvatar } from "../../components/FeishuAvatar";
import { Link } from "react-router-dom";
import { Label } from "../../components/Label";

const BasicInfoCardForAgent = ({ store }: { store: AgentStore }) => {
  useSlot("BasicInfoCard-title", () => "基本信息", []);
  const { data, loading, error, result } = useAgentConfig(store.id);
  if (!data && loading) return <Tip type="loading">Loading...</Tip>;
  if (error) return <Tip type="error">{getErrorMessage(error)}</Tip>;
  return (
    <div className="BasicInfoFieldGrid">
      <div>
        <strong>智能体名称：</strong> <span>{data?.name}</span>
      </div>
      <div>
        <strong>所有者：</strong> <FeishuAvatar uniqId={result?.owner} showId />
      </div>
      {data?.icon && (
        <div>
          <strong>图标：</strong>
          <img
            src={data?.icon}
            style={{
              width: 32,
              height: 32,
              borderRadius: 6,
              border: "1px solid #eee",
              background: "#fafbfc",
            }}
            alt="logo"
          />
        </div>
      )}
      {data?.tags?.length && (
        <div>
          <strong>标签：</strong>{" "}
          <span>
            {data?.tags?.map((tag, index) => (
              <Label key={index}>{tag}</Label>
            ))}
          </span>
        </div>
      )}
      <div style={{ gridColumn: "span 2" }}>
        <strong>描述：</strong> <span>{data?.description}</span>
      </div>
      <div>
        <strong>AI 类型：</strong> <span>{data?.aiType}</span>
      </div>
    </div>
  );
};

const BasicInfoCardForSite = ({ store }: { store: SiteStore }) => {
  useSlot("BasicInfoCard-title", () => "基本信息", []);
  const { data, loading, error, result } = useTagStore(store);
  if (!data && loading) return <Tip type="loading">Loading...</Tip>;
  if (error) return <Tip type="error">{getErrorMessage(error)}</Tip>;
  return (
    <div className="BasicInfoFieldGrid">
      <div>
        <strong>站点名称：</strong> <span>{data?.title || "-"}</span>
      </div>
      {data?.url && (
        <div>
          <strong>站点地址：</strong>{" "}
          <Link to={data?.url} target="_blank">
            {data?.url}
          </Link>
        </div>
      )}
      <div>
        <strong>所有者：</strong> <FeishuAvatar uniqId={result?.owner} showId />
      </div>
      <div style={{ gridColumn: "span 2" }}>
        <strong>站点描述：</strong> {data?.description}
      </div>
    </div>
  );
};

export const BasicInfoCard = ({ store }: { store: AgentStore | SiteStore }) => {
  const content = useMemo(() => {
    switch (true) {
      case store instanceof AgentStore:
        return <BasicInfoCardForAgent store={store} />;
      case store instanceof SiteStore:
        return <BasicInfoCardForSite store={store} />;
      default:
        neverReachHere(store);
        return <Tip type="error">Page Not Found</Tip>;
    }
  }, [store]);
  const title = useSlot("BasicInfoCard-title");
  return (
    <div className="SettingsCard">
      <div>{title}</div>
      <div>{content}</div>
    </div>
  );
};
