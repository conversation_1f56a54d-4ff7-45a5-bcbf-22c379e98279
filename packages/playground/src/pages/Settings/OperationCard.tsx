import {
  AgentStore,
  favoriteAgentsStore,
  favoriteSitesStore,
  myAgentsStore,
  mySitesStore,
  publicAgentsStore,
  publicSitesStore,
  SiteStore,
} from "@hll/quantum-store";
import { useCallback, useMemo, useState } from "react";
import { neverReachHere } from "../../utils/neverReachHere";
import { getErrorMessage } from "../../utils/getErrorMessage";
import { Button } from "../../components/Button";
import { DeleteIcon } from "../../icons/DeleteIcon";
import { showError } from "../../components/Notification";
import { useNavigate } from "react-router-dom";
import { ChevronIcon } from "../../icons/ChevronIcon";
import { Tip } from "../../components/Tip";

export const OperationCard = ({ store }: { store: AgentStore | SiteStore }) => {
  const navigate = useNavigate();

  const { path, name, myStore, publicStore, favoriteStore } = useMemo(() => {
    switch (true) {
      case store instanceof AgentStore:
        return {
          path: "agents",
          name: "智能体",
          myStore: myAgentsStore,
          publicStore: publicAgentsStore,
          favoriteStore: favoriteAgentsStore,
        };
      case store instanceof SiteStore:
        return {
          path: "sites",
          name: "站点导航",
          myStore: mySitesStore,
          publicStore: publicSitesStore,
          favoriteStore: favoriteSitesStore,
        };
      default:
        throw neverReachHere(store);
    }
  }, [store]);

  const handleDelete = useCallback(async () => {
    try {
      const pubs = await publicStore.get();
      if ((pubs.data || []).includes(store.id)) {
        throw new Error("该 Agent 已设置为公开，请先取消公开后再删除");
      }
      await favoriteStore.remove(store.id);
      await myStore.remove(store.id);
      await store.transaction(async ({ head }) => {
        if (!head) throw new Error(`当前${name}（${store.id}）似乎已经不存在了？`);
        await store.delete(head);
      });
      navigate(`/${path}`);
    } catch (error) {
      showError(getErrorMessage(error));
    }
  }, [store, path, name, publicStore, favoriteStore, navigate]);

  const [expanded, setExpanded] = useState(false);
  return (
    <div className="SettingsCard">
      <div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "var(--gap)",
            cursor: "pointer",
            padding: "calc(var(--gap) * 0.5)",
            margin: "calc(var(--gap) * -0.5)",
            borderRadius: "var(--gap)",
          }}
          className="hover-background transition-all"
          onClick={() => setExpanded(!expanded)}
        >
          高级设置
          <ChevronIcon style={{ transform: expanded ? "rotate(0deg)" : "rotate(-90deg)" }} className="transition-all" />
        </div>
      </div>
      <div
        className="OperationList transition-all"
        style={{ visibility: expanded ? "visible" : "hidden", opacity: expanded ? 1 : 0 }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "var(--gap)" }}>
          <Tip type="important">重要提示：删除后将不可恢复，请谨慎操作</Tip>
          <div style={{ flex: 1 }}></div>
          <Button icon={<DeleteIcon />} variant="danger" outlined onClick={handleDelete}>
            删除{name}
          </Button>
        </div>
      </div>
    </div>
  );
};
