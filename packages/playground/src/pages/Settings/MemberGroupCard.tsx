import { useState, useCallback } from "react";
import { FeishuAvatar } from "../../components/FeishuAvatar";
import { Dialog } from "../../components/Dialog";
import { Button } from "../../components/Button";
import { DeleteIcon } from "../../icons/DeleteIcon";
import { TagStore, useTagStore } from "@hll/quantum-store";
import { showError, showSuccess } from "../../components/Notification";
import { useUserInfo } from "../../services/user";
import { AddUserButton } from "./AddUserButton";
import { Tip } from "../../components/Tip";

export const MemberGroupCard = ({ store }: { store: TagStore<unknown> }) => {
  const { data: userInfo } = useUserInfo();
  const { owner, result } = useTagStore(store);
  const members = result?.getAuthorizedUsers() ?? [];
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteId, setDeleteId] = useState("");

  const handleDeleteMember = async (memberId: string) => {
    if (owner !== userInfo?.uniqId) {
      return showError("当前无权限操作");
    }
    try {
      await store.modifyAuthorizedUsers((authorizedUsers) => authorizedUsers.delete(memberId));
      showSuccess("删除成功");
    } catch (e) {
      showError("删除失败");
      return;
    }
  };

  const openDialog = useCallback((id: string) => {
    setShowDeleteDialog(true);
    setDeleteId(id);
  }, []);

  const handleConfirmDelete = useCallback(() => {
    handleDeleteMember(deleteId);
    setShowDeleteDialog(false);
    setDeleteId("");
  }, [deleteId]);

  const handleCancelDelete = useCallback(() => {
    setShowDeleteDialog(false);
    setDeleteId("");
  }, []);

  return (
    <div className="SettingsCard">
      <div>
        <div>成员管理</div>
        <AddUserButton store={store} />
      </div>
      <div className="MemberList">
        {members.length === 0 && <Tip type="empty">暂无成员</Tip>}
        {members.map((memberId) => (
          <div className="MemberItem hover-background transition-all">
            <FeishuAvatar uniqId={memberId} showId />
            <div style={{ flex: 1 }}></div>
            <Button icon={<DeleteIcon />} variant="icon" onClick={() => openDialog(memberId)} />
          </div>
        ))}
      </div>
      <Dialog
        open={showDeleteDialog}
        onClose={handleCancelDelete}
        title="确认删除"
        onOk={handleConfirmDelete}
        onCancel={handleCancelDelete}
        okText="删除"
        cancelText="取消"
        width={400}
      >
        <div className="DeleteConfirmContent">
          <span>确定要删除</span>
          <FeishuAvatar uniqId={deleteId} showId />
          <span>吗？</span>
        </div>
      </Dialog>
    </div>
  );
};
