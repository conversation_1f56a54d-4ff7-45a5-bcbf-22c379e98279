.Settings {
  height: 100vh;
  background: #f8fafc;
  display: flex;
}

.SettingsCard {
  display: flex;
  flex-direction: column;
  > div:first-child {
    font-size: 1.1em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--gap);
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: var(--gap);
    padding-bottom: var(--gap);
  }
  .BasicInfoFieldGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: calc(var(--gap) * 1);
    color: #555;
    font-size: 0.9em;
    > div {
      display: flex;
      align-items: center;
    }
  }

  .MemberList {
    display: flex;
    flex-direction: column;
    gap: var(--gap);
  }
  .MemberItem {
    display: flex;
    align-items: center;
    gap: calc(var(--gap) * 2);
    padding: var(--card-padding-half);
    border: 1px solid #e5e7eb;
    border-radius: 8px;
  }
}
