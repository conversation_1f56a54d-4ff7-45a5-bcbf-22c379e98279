import { useLocation } from "react-router-dom";
import { MainHeader } from "../../components/MainHeader";
import { getSiteStore, getAgentStore, AgentStore, SiteStore } from "@hll/quantum-store";
import { MemberGroupCard } from "./MemberGroupCard";
import { useSlot } from "@hll/use-slot";
import { BasicInfoCard } from "./BasicInfoCard";
import { Tip } from "../../components/Tip";
import { ManagerMenu } from "../../components/ManagerMenu";
import { OperationCard } from "./OperationCard";
import { Label } from "../../components/Label";

import "./index.css";

export const Settings = () => {
  useSlot("AsideHeader-title", () => "Settings", []);

  return (
    <div className="Settings" style={{ display: "flex" }}>
      <ManagerMenu />
      <div style={{ flex: 1, display: "flex", flexDirection: "column" }}>
        <MainHeader />
        <div
          style={{
            padding: "var(--card-padding)",
            flex: 1,
            overflow: "auto",
            display: "flex",
            flexDirection: "column",
            gap: "calc(var(--gap) * 3)",
          }}
        >
          <Groups />
        </div>
      </div>
    </div>
  );
};

const Groups = () => {
  let store: AgentStore | SiteStore | undefined;
  let name: string | undefined;
  const { pathname } = useLocation();
  const [, type, id] = pathname.split("/");

  switch (type) {
    case "agents":
      store = getAgentStore(id);
      name = "智能体";
      break;
    case "sites":
      store = getSiteStore(id);
      name = "站点导航";
      break;
  }

  useSlot(
    "MainHeader-title",
    () => {
      return (
        <div style={{ display: "flex", alignItems: "center", gap: "var(--gap)" }}>
          {name}设置
          <Label>ID: {id}</Label>
        </div>
      );
    },
    [name, id]
  );

  if (!store) return <Tip type="error">Page Not Found</Tip>;

  return (
    <>
      <BasicInfoCard store={store} />
      <MemberGroupCard store={store} />
      <OperationCard store={store} />
    </>
  );
};
