import { Link } from "react-router-dom";
import { AgentStore, type FinalQuantumAgentConfig, type TagResultNx } from "@hll/quantum-store";

export const AgentView = ({
  agent,
  style,
  ...props
}: {
  agent: TagResultNx<FinalQuantumAgentConfig, AgentStore>;
} & React.HTMLAttributes<HTMLAnchorElement>) => {
  const { data, store } = agent;
  const { name, icon } = data;
  const url = encodeURIComponent(store.base);
  return (
    <Link style={{ ...style, display: "flex", alignItems: "center", gap: ".5ch" }} to={`/chat?agent=${url}`} {...props}>
      {icon ? <img src={icon} alt={name} style={{ width: 16, height: 16, marginRight: 4 }} /> : null}
      {name}
    </Link>
  );
};
