import { useTagStore, favoriteAgentsStore, publicAgentsStore, useAllAgentConfigs } from "@hll/quantum-store";
import { Tip } from "../../components/Tip";
import { Button } from "../../components/Button";
import { DeleteIcon } from "../../icons/DeleteIcon";
import { AgentView } from "./AgentView";
import { getErrorMessage } from "../../utils/getErrorMessage";
import { Tooltip } from "../../components/Tooltip";
import { showError, showSuccess } from "../../components/Notification";
import { useMemo } from "react";

export const MyFavorateAgents = () => {
  const { data: favs, loading: loading1, error: error1 } = useAllAgentConfigs(favoriteAgentsStore);
  const { data: pubs, loading: loading2, error: error2 } = useTagStore(publicAgentsStore);
  const loading = loading1 || loading2;
  const error = error1 || error2;

  const handleRemoveFavorite = async (agentId: string) => {
    try {
      await favoriteAgentsStore.remove(agentId);
      showSuccess("已取消关注");
    } catch (error) {
      showError(error, "取消关注失败");
    }
  };

  const content = useMemo(() => {
    if (!favs && loading) return <Tip type="loading">加载中</Tip>;
    if (error) return <Tip type="error">{getErrorMessage(error)}</Tip>;
    if (favs && favs.length === 0) return <Tip type="empty">暂无关注</Tip>;
    const pubSet = new Set(pubs);
    return favs?.map((agent) => {
      const down = !pubSet.has(agent.store.id);
      return (
        <li key={agent.store.id}>
          <AgentView agent={agent} style={{ opacity: down ? 0.5 : 1, pointerEvents: down ? "none" : "auto" }} />
          {down && (
            <div style={{ marginTop: "8px", display: "flex", alignItems: "center", gap: "8px" }}>
              <Tooltip content={<div style={{ width: 200 }}>该智能体已下架，暂时无法使用，点击可从关注列表中移除</div>}>
                <Button
                  variant="icon"
                  icon={<DeleteIcon />}
                  onClick={() => handleRemoveFavorite(agent.store.id)}
                  title="取消关注"
                />
              </Tooltip>
            </div>
          )}
        </li>
      );
    });
  }, [favs, loading, error, pubs]);

  return <ul className={`MyFavorateAgents ${loading ? "loading" : ""}`}>{content}</ul>;
};
