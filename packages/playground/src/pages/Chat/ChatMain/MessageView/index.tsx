import { BufferState, ChatMessageRole, type ChatMessage } from "@hll/section-parser";
import { useState, useEffect } from "react";
import { SectionView } from "../SectionView";
import { Button } from "../../../../components/Button";

import "./index.css";
import { getErrorMessage } from "../../../../utils/getErrorMessage";
import { Tip } from "../../../../components/Tip";

interface MessageViewProps {
  message: ChatMessage;
  index: number;
  onRewind: () => void;
}

export const MessageView = ({ message, index, onRewind }: MessageViewProps) => {
  const [, render] = useState<unknown>();

  useEffect(() => {
    const controller = {
      enqueue: render,
      error: render,
    };
    message.sections.tee(controller);
    return () => message.sections.untee(controller);
  }, [message]);

  const { sections } = message;

  const classes = ["MessageView"];
  if (sections.state === BufferState.Open) classes.push("generating");
  classes.push(message.role);

  const isWelcomeMessage = index === 0 && message.role === ChatMessageRole.Assistant;

  return (
    <div key={message.index} className={classes.join(" ")}>
      <div className="sections">
        {sections.state === BufferState.Open && sections.buffer.length === 0 ? <Tip type="loading"></Tip> : null}
        {sections.buffer.map((section, index) => (
          <SectionView section={section} key={index} />
        ))}
      </div>
      {sections.reason ? <Tip type="error">{getErrorMessage(sections.reason)}</Tip> : null}
      {isWelcomeMessage ? null : (
        <div className="operations">
          {message.role === ChatMessageRole.Assistant ? (
            <Button onClick={() => onRewind()}>重试</Button>
          ) : (
            <Button onClick={() => onRewind()}>编辑</Button>
          )}
        </div>
      )}
    </div>
  );
};
