import type { TagResultNx, AgentStore, FinalQuantumAgentConfig } from "@hll/quantum-store";
import { useEventListener } from "@hll/use-event-listener";
import { useMemo, useState } from "react";

const ALL_CATEGORY = "all";

const CATEGORIES = [
  { id: ALL_CATEGORY, name: "全部" },
  { id: "development", name: "研发" },
  { id: "operation", name: "运营" },
  { id: "design", name: "设计" },
  { id: "product", name: "产品" },
];

// 筛选函数
const createFilterFunction = (searchQuery: string, activeCategory: string) => {
  return (result: TagResultNx<FinalQuantumAgentConfig, AgentStore>) => {
    const { data } = result;
    const { name = "", description = "", tags = [] } = data || {};

    const matchSearch =
      !searchQuery ||
      name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      description.toLowerCase().includes(searchQuery.toLowerCase());

    const categoryName = CATEGORIES.find((cat) => cat.id === activeCategory)?.name || "";
    const matchCategory =
      activeCategory === ALL_CATEGORY || tags.some((tag: string) => tag.toLowerCase() === categoryName.toLowerCase());

    return matchSearch && matchCategory;
  };
};

export class MarketFilterManager {
  #activeCategory: string = ALL_CATEGORY;
  #searchQuery: string = "";

  public eventBus = new EventTarget();

  get activeCategory(): string {
    return this.#activeCategory;
  }

  get searchQuery(): string {
    return this.#searchQuery;
  }

  get categories() {
    return CATEGORIES;
  }

  public getFilterFunction(this: this) {
    return createFilterFunction(this.#searchQuery, this.#activeCategory);
  }

  public setActiveCategory(this: this, category: string) {
    this.#activeCategory = category;
    this.eventBus.dispatchEvent(new Event("activeCategoryUpdate"));
  }

  public setSearchQuery(this: this, query: string) {
    this.#searchQuery = query;
    this.eventBus.dispatchEvent(new Event("searchQueryUpdate"));
  }
}

// React Hook 包装器
export function useMarketFilterManager() {
  const manager = useMemo(() => new MarketFilterManager(), []);
  const [, render] = useState({});
  useEventListener("activeCategoryUpdate", () => render({}), { target: manager.eventBus });
  useEventListener("searchQueryUpdate", () => render({}), { target: manager.eventBus });
  return manager;
}
