.Dropdown {
  position: relative;
  display: inline-flex;
}

.Dropdown-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border: 1px solid transparent;
  border-radius: 6px;
  background: transparent;
  color: #8c8c8c;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  font-size: 14px;
  line-height: 1.5715;
}

.Dropdown-trigger:hover:not(.disabled) {
  color: #595959;
  background-color: rgba(0, 0, 0, 0.04);
  border-color: transparent;
}

.Dropdown-trigger.active:not(.disabled) {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  border-color: transparent;
}

.Dropdown-trigger:focus:not(.disabled) {
  outline: none;
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  border-color: transparent;
}

.Dropdown-trigger.disabled {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
  background-color: rgba(0, 0, 0, 0.04);
}

.Dropdown-menu {
  min-width: 160px;
  margin: -8px -16px;
}

.Dropdown-menu-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: rgba(0, 0, 0, 0.88);
  font-weight: normal;
  font-size: 14px;
  line-height: 1.5714285714285714;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  border-radius: 6px;
  clear: both;
  white-space: nowrap;
  list-style: none;
}

.Dropdown-menu-item:hover:not(.disabled):not(.active) {
  background-color: rgba(0, 0, 0, 0.04);
}

.Dropdown-menu-item.danger {
  color: #ff4d4f;
}

.Dropdown-menu-item.danger:hover:not(.disabled) {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.Dropdown-menu-item.disabled {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
  background-color: transparent;
}

.Dropdown-menu-item.disabled:hover {
  background-color: transparent;
}

.Dropdown-menu-title {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.Dropdown-menu-item-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.Dropdown-menu-item.active {
  color: #fff;
  font-weight: 600;
  background-color: var(--primary);
  cursor: default;
}

.Dropdown-menu-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 4px 0;
}

.Dropdown-menu-item.has-submenu {
  position: relative;
}

.Dropdown-menu-arrow {
  margin-left: auto;
  font-size: 10px;
  color: #8c8c8c;
  transition: transform 0.2s;
}

.Dropdown-menu-item.has-submenu:hover .Dropdown-menu-arrow {
  transform: rotate(90deg);
}

.Dropdown-submenu {
  position: absolute;
  left: 100%;
  top: 0;
  min-width: 160px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  margin-left: 4px;
}

.Dropdown-menu-title-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: #595959;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.5714285714285714;
  border-radius: 0;
  clear: both;
  white-space: nowrap;
  list-style: none;
  cursor: default;
}

.Dropdown-menu-item.submenu {
  padding-left: 32px;
}
