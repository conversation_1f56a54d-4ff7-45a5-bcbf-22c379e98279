import React, { useState, type ReactNode } from "react";
import { Popover } from "../Popover";
import "./index.css";
import { LoadingIcon } from "../../icons/LoadingIcon";

export interface DropdownItem {
  key: string;
  label?: ReactNode;
  icon?: ReactNode;
  disabled?: boolean;
  danger?: boolean;
  active?: boolean; // 新增
  onClick?: () => Promise<void> | void;
  type?: "divider" | "title"; // 新增 title 类型
  children?: DropdownItem[]; // 新增：支持子菜单
  submenu?: boolean; // 新增：标识为子菜单项
}

export interface DropdownProps {
  trigger?: ReactNode;
  items: DropdownItem[];
  placement?: "top" | "bottom" | "left" | "right" | "topLeft" | "topRight" | "bottomLeft" | "bottomRight";
  triggerClassName?: string;
  menuClassName?: string;
  disabled?: boolean;
  onVisibleChange?: (visible: boolean) => void;
}

export const Dropdown: React.FC<DropdownProps> = ({
  trigger,
  items,
  placement = "bottom",
  menuClassName = "",
  disabled = false,
  onVisibleChange,
}) => {
  // 菜单内容
  const menu = (
    <div className={`Dropdown-menu ${menuClassName}`}>
      {items.map((item) => (
        <MenuItem key={item.key} item={item} />
      ))}
    </div>
  );

  return (
    <Popover
      trigger={trigger}
      content={menu}
      placement={placement}
      triggerClassName="Dropdown-trigger"
      popoverClassName="Dropdown-popover"
      disabled={disabled}
      onVisibleChange={onVisibleChange}
    />
  );
};

const MenuItem = ({ item }: { item: DropdownItem }) => {
  const [loading, setLoading] = useState(false);
  const [showSubmenu, setShowSubmenu] = useState(false);

  if (item.type === "divider") {
    return <div key={item.key} className="Dropdown-menu-divider" />;
  }

  if (item.type === "title") {
    return (
      <div key={item.key} className="Dropdown-menu-title-item">
        {item.icon && <span className="Dropdown-menu-item-icon">{item.icon}</span>}
        <span className="Dropdown-menu-title">{item.label}</span>
      </div>
    );
  }

  const handleItemClick = async () => {
    if (item.disabled) return;
    if (loading) return;
    if (item.active) return; // active 状态时不执行点击
    if (item.children && item.children.length > 0) return; // 有子菜单时不执行点击
    try {
      const result = item.onClick?.();
      if (result instanceof Promise) {
        try {
          setLoading(true);
          await result;
        } finally {
          setLoading(false);
        }
      }
    } catch (error) {
      console.error("Dropdown MenuItem onClick error:", error);
    }
  };

  const hasSubmenu = item.children && item.children.length > 0;

  return (
    <div
      key={item.key}
      className={`Dropdown-menu-item ${item.danger ? "danger" : ""} ${item.disabled || loading ? "disabled" : ""} ${
        item.active ? "active" : ""
      } ${hasSubmenu ? "has-submenu" : ""} ${item.submenu ? "submenu" : ""}`}
      onClick={handleItemClick}
      onMouseEnter={() => hasSubmenu && setShowSubmenu(true)}
      onMouseLeave={() => hasSubmenu && setShowSubmenu(false)}
    >
      {item.icon && <span className="Dropdown-menu-item-icon">{loading ? <LoadingIcon /> : item.icon}</span>}
      <span className="Dropdown-menu-title">{item.label}</span>
      {hasSubmenu && (
        <>
          <span className="Dropdown-menu-arrow">▶</span>
          {showSubmenu && (
            <div className="Dropdown-submenu">
              {item.children!.map((childItem) => (
                <MenuItem key={childItem.key} item={childItem} />
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
};
