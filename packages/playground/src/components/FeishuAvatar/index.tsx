import { useEffect, useState } from "react";
import { getFeishuUserInfoById, type UserInfo } from "../../services/user";
import "./index.css";

interface FeishuAvatarProps {
  uniqId?: string;
  user?: UserInfo;
  noLink?: boolean;
  showId?: boolean;
}

export const FeishuAvatar = ({ uniqId, user, noLink, showId = false }: FeishuAvatarProps) => {
  const [internalInfo, setInternalInfo] = useState<UserInfo | null>(null);
  const [internalLoading, setInternalLoading] = useState(!user);

  const userInfo = user || internalInfo;

  useEffect(() => {
    if (!uniqId) return;
    let cancelled = false;
    setInternalLoading(true);
    getFeishuUserInfoById([uniqId]).then((info) => {
      if (!cancelled) {
        setInternalInfo(info[uniqId] || null);
        setInternalLoading(false);
      }
    });
    return () => {
      cancelled = true;
    };
  }, [uniqId]);

  if (!userInfo && internalLoading) {
    return (
      <div className="FeishuAvatar FeishuAvatar--loading">
        <div className="FeishuAvatar__skeleton-avatar"></div>
        <div className="FeishuAvatar__skeleton-text"></div>
        {showId && <div className="FeishuAvatar__skeleton-id"></div>}
      </div>
    );
  }

  const src = userInfo?.avatar72;
  const name = userInfo?.name.replace(/\(.*\)$/, "") || uniqId;
  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (userInfo?.openId) {
      window.open(`lark://applink.feishu.cn/client/chat/open?openId=${userInfo.openId}`);
    }
  };

  return (
    <div onClick={noLink ? undefined : handleClick} className={`FeishuAvatar ${noLink ? "no-link" : ""}`}>
      {src && <img src={src} alt="avatar" />}
      <span>{name}</span>
      {showId && <span>({uniqId})</span>}
    </div>
  );
};
