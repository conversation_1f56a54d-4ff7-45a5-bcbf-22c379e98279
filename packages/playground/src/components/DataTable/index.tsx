import { useMemo, type ReactNode } from "react";
import { Tip } from "../Tip";
import "./index.css";
import { u2o, u2s } from "u2x";

export interface Column<T = unknown> {
  key: string;
  title: string;
  width?: string;
  align?: "left" | "center" | "right";
  render?: (value: unknown, record: T, index: number) => ReactNode;
}

export interface DataTableProps<T = unknown> {
  data: T[];
  columns: Column<T>[];
  emptyText?: string;
  className?: string;
  rowKey?: string | ((record: T, index: number) => string);
}

export const DataTable = <T extends unknown>({
  data,
  columns,
  emptyText = "暂无数据",
  className = "",
  rowKey,
}: DataTableProps<T>) => {
  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === "function") {
      return rowKey(record, index);
    }
    if (typeof rowKey === "string") {
      return u2s(u2o(record)[rowKey]);
    }
    return index.toString();
  };

  const content = useMemo(() => {
    if (data.length === 0) {
      return <Tip type="empty">{emptyText}</Tip>;
    }
    return <Table columns={columns} data={data} getRowKey={getRowKey} />;
  }, [data, columns, getRowKey, emptyText]);

  return <div className={`DataTable ${className}`}>{content}</div>;
};

const Table = <T extends unknown>({
  columns,
  data,
  getRowKey,
}: {
  columns: Column<T>[];
  data: T[];
  getRowKey: (record: T, index: number) => string;
}) => {
  return (
    <table>
      <thead>
        <tr>
          {columns.map((column) => (
            <th
              key={column.key}
              style={{
                width: column.width,
                textAlign: column.align || "left",
              }}
            >
              {column.title}
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {data.map((record, index) => (
          <tr key={getRowKey(record, index)}>
            {columns.map((column) => (
              <td key={column.key} style={{ textAlign: column.align || "left" }}>
                {column.render ? column.render(u2o(record)[column.key], record, index) : u2s(u2o(record)[column.key])}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );
};
