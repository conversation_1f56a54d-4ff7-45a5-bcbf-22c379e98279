import React, { useEffect, useRef, useState } from "react";
import ReactDOM from "react-dom";
import "./index.css";
import { Button } from "../Button";

interface DialogProps {
  width?: number;
  open: boolean;
  onClose: () => void;
  title?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
  okText?: React.ReactNode;
  cancelText?: React.ReactNode;
  onOk?: () => void | Promise<void>;
  onCancel?: () => void;
  confirmLoading?: boolean;
  closable?: boolean;
  maskClosable?: boolean;
  mask?: boolean;
  footer?: React.ReactNode | null;
}

const ANIMATION_DURATION = 250; // ms

export const Dialog: React.FC<DialogProps & React.HTMLAttributes<HTMLDivElement>> = ({
  width,
  open,
  onClose,
  title,
  children,
  className = "",
  okText = "确定",
  cancelText = "取消",
  onOk,
  onCancel,
  confirmLoading = false,
  closable = true,
  maskClosable = true,
  mask = true,
  footer,
  ...rest
}) => {
  const [visible, setVisible] = useState(open);
  const closeTimer = useRef<number | null>(null);

  useEffect(() => {
    if (open) {
      setVisible(true);
    } else if (visible) {
      closeTimer.current = window.setTimeout(() => {
        setVisible(false);
      }, ANIMATION_DURATION);
    }
    return () => {
      if (closeTimer.current) clearTimeout(closeTimer.current);
    };
  }, [open]);

  useEffect(() => {
    if (!open) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [open, onClose]);

  if (!visible) return null;

  const handleCancel = () => {
    onCancel?.();
    onClose();
  };
  const handleOk = async () => {
    if (onOk) await onOk();
  };

  const dialogFooter =
    footer === null ? null : footer !== undefined ? (
      footer
    ) : (
      <div className="Dialog-footer">
        <Button onClick={handleCancel}>{cancelText}</Button>
        <Button loading={confirmLoading} variant="primary" onClick={handleOk}>
          {okText}
        </Button>
      </div>
    );

  const portalClass = `Dialog-portal Dialog-portal--${open ? "open" : "close"}`;
  const backdropClass = `Dialog-backdrop Dialog-backdrop--${open ? "open" : "close"}`;
  const dialogClass = `Dialog ${open ? "Dialog--open" : "Dialog--close"} ${className}`;

  const dialog = (
    <div className={portalClass}>
      {mask && <div className={backdropClass} onClick={maskClosable ? onClose : undefined} />}
      <div className={dialogClass} role="dialog" aria-modal="true" style={{ width }}>
        {title && <div className="Dialog-title">{title}</div>}
        {closable && (
          <button className="Dialog-close" onClick={onClose} aria-label="Close">
            ×
          </button>
        )}
        <div className="Dialog-content" {...rest}>
          {children}
        </div>
        {dialogFooter}
      </div>
    </div>
  );

  return typeof window !== "undefined" ? ReactDOM.createPortal(dialog, document.body) : null;
};
