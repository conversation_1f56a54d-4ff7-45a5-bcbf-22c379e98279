.<PERSON><PERSON> {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #ffffff;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  min-height: 32px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5ch;
}

/* Size variants */
.Button--small {
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 4px;
  min-height: 24px;
  line-height: 1;
}

.Button--large {
  padding: 12px 20px;
  font-size: 16px;
  border-radius: 8px;
  min-height: 40px;
  line-height: 1;
}

.Button:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.Button:active:not(:disabled) {
  background-color: #f3f4f6;
  transform: translateY(1px);
}

.Button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.Button--primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: #ffffff;
}

.Button--primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.Button--primary:active:not(:disabled) {
  background-color: var(--primary-active);
}

.Button--danger {
  background-color: #ef4444;
  border-color: #ef4444;
  color: #ffffff;
}

.Button--danger:hover:not(:disabled) {
  background-color: #dc2626;
  border-color: #dc2626;
}

.Button--danger:active:not(:disabled) {
  background-color: #b91c1c;
}

.Button--link {
  padding: 0;
  border: none;
  background-color: transparent;
  color: #3b82f6;
  text-decoration: none;
  font-weight: normal;
}

.Button--link:hover:not(:disabled) {
  background-color: transparent;
  color: #2563eb;
  text-decoration: none;
}

.Button--link:active:not(:disabled) {
  background-color: transparent;
  transform: none;
}

.Button--icon {
  padding: 2px;
  border: none;
  background-color: transparent;
  color: #6b7280;
  border-radius: 6px;
  min-width: auto;
  min-height: auto;
  font-size: 1.1em;
}

.Button--icon:hover:not(:disabled) {
  background-color: #f3f4f6;
  color: #374151;
}

.Button--icon:active:not(:disabled) {
  background-color: #e5e7eb;
  transform: none;
}

.Button--outlined {
  background-color: transparent;
  border-width: 1px;
  color: var(--primary);
  border-color: var(--primary);
}
.Button--outlined.Button--danger {
  color: #ef4444;
  border-color: #ef4444;
}
.Button--outlined.Button--danger:hover:not(:disabled) {
  background-color: #ef4444;
  color: #fff;
  border-color: #ef4444;
}
.Button--outlined:hover:not(:disabled) {
  background-color: var(--primary);
  color: #fff;
  border-color: var(--primary);
}

.Button-icon {
  display: flex;
  align-items: center;
}

.Button svg {
  height: 16px;
  width: 16px;
  vertical-align: middle;
  display: inline-block;
}

.button-skeleton {
  position: relative;
  overflow: hidden;
  background: #eee;
}
.button-skeleton::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, #eee 25%, #f5f5f5 50%, #eee 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.2s infinite linear;
  z-index: 1;
  pointer-events: none;
}
.button-skeleton > span {
  visibility: hidden;
}
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
