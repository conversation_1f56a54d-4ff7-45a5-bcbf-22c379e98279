import { ErrorIcon } from "../../icons/ErrorIcon";
import { LoadingIcon } from "../../icons/LoadingIcon";
import { EmptyIcon } from "../../icons/EmptyIcon";
import { WarningIcon } from "../../icons/WarningIcon";

import "./index.css";

export type TipType = "loading" | "error" | "empty" | "weak" | "important";

const getIcon = (type: TipType) => {
  if (type === "loading") return <LoadingIcon />;
  if (type === "error") return <ErrorIcon />;
  if (type === "important") return <WarningIcon />;
  if (type === "empty") return <EmptyIcon />;
  return null;
};

interface TipProps extends React.HTMLAttributes<HTMLDivElement> {
  type: TipType;
}

export function Tip(props: TipProps): React.ReactNode;
export function Tip({ type, children, className = "", ...rest }: TipProps) {
  return (
    <div className={`Tip ${type} ${className}`} {...rest}>
      {getIcon(type)}
      <span>{children}</span>
    </div>
  );
}
