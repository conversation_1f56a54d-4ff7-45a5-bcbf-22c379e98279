import { Link, useLocation, useNavigate } from "react-router-dom";
import { LogoIcon } from "../../../icons/LogoIcon";
import { Dropdown } from "../../Dropdown";
import { MarketIcon } from "../../../icons/MarketIcon";
import { MenuIcon } from "../../../icons/MenuIcon";
import { useSlot } from "@hll/use-slot";
import { navigationSections } from "../../ManagerMenu";

import "./index.css";

interface MenuItem {
  label?: string;
  icon?: React.ReactNode;
  key: string;
  href?: string;
  type?: "divider" | "title";
  submenu?: boolean;
}

const menuItems: MenuItem[] = [
  { label: "智能体市场", icon: <MarketIcon />, key: "market", href: "/chat" },
  { type: "divider", key: "divider1" },
  { type: "title", label: "智能体管理", icon: navigationSections.agents.icon, key: "agents-header" },
  ...navigationSections.agents.items.map((item) => ({
    label: item.label,
    icon: item.icon,
    key: item.path,
    href: item.path,
    submenu: true,
  })),
  { type: "title", label: "网址导航管理", icon: navigationSections.sites.icon, key: "sites-header" },
  ...navigationSections.sites.items.map((item) => ({
    label: item.label,
    icon: item.icon,
    key: item.path,
    href: item.path,
    submenu: true,
  })),
];

export const AsideHeader = () => {
  const navigate = useNavigate();
  const title = useSlot("AsideHeader-title");
  const { pathname } = useLocation();
  return (
    <div className="AsideHeader">
      <Link to="/" className="logo-link">
        <LogoIcon width={36} height={36} style={{ marginLeft: -6 }} />
      </Link>
      <div>{title}</div>
      <div style={{ flex: 1 }}></div>
      <Dropdown
        trigger={<MenuIcon />}
        items={menuItems.map((item) => {
          if (item.type === "divider") {
            return {
              key: item.key,
              type: "divider" as const,
            };
          }
          if (item.type === "title") {
            return {
              key: item.key,
              type: "title" as const,
              label: item.label,
              icon: item.icon,
            };
          }
          return {
            label: item.label,
            icon: item.icon,
            key: item.key,
            active: pathname === item.href,
            onClick: () => {
              if (item.href) {
                navigate(item.href);
              }
            },
            submenu: item.submenu,
          };
        })}
      />
    </div>
  );
};
