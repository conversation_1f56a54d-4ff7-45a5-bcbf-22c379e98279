import React from "react";

export interface UnreadBadgeProps<T = unknown> extends React.HTMLAttributes<HTMLSpanElement> {
  /** 消息数组 */
  data: T[] | undefined | null;
  /** 判断消息是否未读的方法，返回 true 表示未读 */
  isUnread: (item: T) => boolean;
  /** 是否显示具体数量，false 时只显示小红点 */
  showCount?: boolean;
  /** 没有未读消息时是否隐藏 */
  hideWhenZero?: boolean;
}

export function UnreadBadge<T = unknown>({
  data,
  isUnread,
  showCount = true,
  hideWhenZero = true,
  className,
  style,
  ...props
}: UnreadBadgeProps<T>) {
  const unreadCount = Array.isArray(data) ? data.filter(isUnread).length : 0;

  if (hideWhenZero && unreadCount === 0) return null;

  // 只显示小红点
  if (!showCount) {
    return (
      <span
        {...props}
        className={className}
        style={{
          display: "inline-block",
          width: 8,
          height: 8,
          borderRadius: "50%",
          background: "red",
          verticalAlign: "middle",
          ...style,
        }}
        title={unreadCount > 0 ? `有未读消息` : undefined}
      />
    );
  }

  // 显示数量
  return (
    <span
      {...props}
      className={className}
      style={{
        background: "red",
        color: "white",
        borderRadius: "50%",
        fontSize: "0.8em",
        width: "1.2em",
        height: "1.2em",
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center",
        ...style,
      }}
      title={unreadCount > 0 ? `有 ${unreadCount} 条消息` : undefined}
    >
      {unreadCount}
    </span>
  );
}

export default UnreadBadge;
