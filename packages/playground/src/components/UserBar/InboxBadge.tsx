import { getInboxStore, useTagStore } from "@hll/quantum-store";
import UnreadBadge from "../UnreadBadge";
import { useEffect } from "react";

export const InboxBadge = ({ children }: { children: React.ReactNode }) => {
  const inbox = getInboxStore("-");
  const { data, error } = useTagStore(inbox);

  useEffect(() => {
    if (!error) return;
    console.error(error);
  }, [error]);

  return (
    <div style={{ position: "relative", display: "flex", alignItems: "center" }}>
      <UnreadBadge data={data} isUnread={() => true} style={{ position: "absolute", top: "-.8em", right: "-.8em" }} />
      {children}
    </div>
  );
};
