import type { Me } from "../../services/user";
import { Tip } from "../Tip";

export const UserInfo = ({ user }: { user: Me | undefined }) => {
  if (!user) return null;
  const avatarUrl = user.avatar72;
  return (
    <div style={{ display: "flex", alignItems: "center", gap: "0.75em" }}>
      {avatarUrl ? (
        <img
          src={avatarUrl}
          alt={user.name || user.uniqId}
          style={{
            width: "32px",
            height: "32px",
            borderRadius: "100%",
            objectFit: "cover",
          }}
        />
      ) : null}
      <div style={{ display: "flex", flexDirection: "column", minWidth: 0 }}>
        <div
          style={{
            textOverflow: "ellipsis",
            overflow: "hidden",
            whiteSpace: "nowrap",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            gap: "var(--gap)",
          }}
        >
          <strong>{user.name || user.uniqId}</strong>
          <Tip type="weak">{user.title}</Tip>
        </div>
        <div
          style={{
            color: "#888",
            fontSize: "0.85em",
            textOverflow: "ellipsis",
            overflow: "hidden",
            whiteSpace: "nowrap",
          }}
        >
          {user.department}
        </div>
      </div>
    </div>
  );
};
