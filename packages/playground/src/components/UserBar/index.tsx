import { FeishuAvatar } from "../FeishuAvatar";
import { Tip } from "../Tip";
import { useUserInfo } from "../../services/user";
import { getErrorMessage } from "../../utils/getErrorMessage";
import { Popover } from "../Popover";
import { InboxBadge } from "./InboxBadge";
import { InboxList } from "./InboxList";
import { UserInfo } from "./UserInfo";

import "./index.css";

export const UserBar = () => {
  const { data, error } = useUserInfo();

  if (error) {
    return <Tip type="error">{getErrorMessage(error)}</Tip>;
  }

  const popoverContent = (
    <div style={{ display: "flex", flexDirection: "column", gap: "calc(var(--gap) * 1)" }}>
      <UserInfo user={data} />
      <hr style={{ border: "none", height: 1, background: "#eee", width: "100%", margin: 0 }} />
      <InboxList />
    </div>
  );

  return (
    <div className="UserBar">
      <Popover
        trigger={
          <InboxBadge>
            <FeishuAvatar user={data} noLink />
          </InboxBadge>
        }
        content={popoverContent}
        placement="bottomRight"
        popoverClassName="UserBar-popover"
        triggerClassName="UserBar-trigger"
      />
    </div>
  );
};
