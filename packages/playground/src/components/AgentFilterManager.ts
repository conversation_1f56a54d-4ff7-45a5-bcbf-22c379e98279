import { useState, useMemo } from "react";
import { u2a, u2o, u2s } from "u2x";

export interface Category {
  id: string;
  name: string;
}

export interface AgentFilterManagerOptions<T> {
  categories: Category[];
  defaultCategory: string;
  getAgentData: (agentId: string) => T | undefined;
  filterFunction?: (agent: T | undefined, searchQuery: string, activeCategory: string) => boolean;
}

export function useAgentFilterManager<T = unknown>(agentIds: string[], options: AgentFilterManagerOptions<T>) {
  const {
    categories,
    defaultCategory,
    getAgentData,
    filterFunction = (agent, searchQuery, activeCategory) => {
      if (!agent) return false;
      const { name, description, tags } = u2o(agent);
      const matchSearch =
        !searchQuery ||
        u2s(name)?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        u2s(description)?.toLowerCase().includes(searchQuery.toLowerCase());
      const matchCategory = activeCategory === "all" || u2a(tags).includes(activeCategory);
      return matchSearch && matchCategory;
    },
  } = options;

  const [activeCategory, setActiveCategory] = useState(defaultCategory);
  const [searchQuery, setSearchQuery] = useState("");

  const filteredAgentIds = useMemo(() => {
    return agentIds.filter((agentId) => {
      const agent = getAgentData(agentId);
      return filterFunction(agent, searchQuery, activeCategory);
    });
  }, [agentIds, getAgentData, filterFunction, searchQuery, activeCategory]);

  return {
    categories,
    activeCategory,
    setActiveCategory,
    searchQuery,
    setSearchQuery,
    filteredAgentIds,
  };
}
