import { ListTagStore, useTagStore } from "@hll/quantum-store";
import { Button } from "./Button";
import { showError } from "./Notification";
import { Tooltip } from "./Tooltip";
import { VisibilityIcon } from "../icons/VisibilityIcon";
import { VisibilityOffIcon } from "../icons/VisibilityOffIcon";

export const PublicOrPrivateButton = ({ id, listTagStore }: { id: string; listTagStore: ListTagStore<string> }) => {
  const { data, loading, error } = useTagStore(listTagStore);
  const isPublic = data?.includes(id) ?? null;

  const togglePrivateOrPublic = async () => {
    if (isPublic == null) return;
    try {
      if (isPublic) {
        await listTagStore.remove(id);
      } else {
        await listTagStore.add(id);
      }
    } catch (e) {
      showError(e, "设置失败");
    }
  };

  if (error) {
    return null;
  }

  return (
    <Tooltip
      content={
        isPublic ? (
          <>
            当前为 <strong>公开</strong> 状态，点击设为 <strong>私有</strong>
          </>
        ) : (
          <>
            当前为 <strong>私有</strong> 状态，点击设为 <strong>公开</strong>
          </>
        )
      }
      placement="bottom"
    >
      <Button
        skeleton={loading}
        onClick={togglePrivateOrPublic}
        icon={isPublic ? <VisibilityIcon /> : <VisibilityOffIcon />}
      />
    </Tooltip>
  );
};
