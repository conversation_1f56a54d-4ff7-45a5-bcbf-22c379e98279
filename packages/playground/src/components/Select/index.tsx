import React, { useState, useRef, useEffect } from "react";
import ReactDOM from "react-dom";
import { LoadingIcon } from "../../icons/LoadingIcon";

import "./index.css";

export interface Option {
  label: string;
  value: string | number;
  disabled?: boolean;
}

export interface SelectProps {
  options: Option[];
  value?: string | number;
  onChange?: (value: string | number, option: Option) => void;
  placeholder?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  showSearch?: boolean;
  onSearch?: (keyword: string) => void;
  loading?: boolean;
}

export const Select: React.FC<SelectProps> = ({
  options,
  value,
  onChange,
  placeholder = "请选择",
  disabled = false,
  style,
  showSearch = false,
  onSearch,
  loading = false,
}) => {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [dropdownPos, setDropdownPos] = useState({ left: 0, top: 0, width: 0 });
  const ref = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLUListElement>(null);

  useEffect(() => {
    if (!open) return;
    const handleClick = (e: MouseEvent) => {
      // 判断点击是否在 select 或 dropdown 区域
      const target = e.target as Node;
      if (
        (ref.current && ref.current.contains(target)) ||
        (dropdownRef.current && dropdownRef.current.contains(target))
      ) {
        return;
      }
      setOpen(false);
    };
    document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [open]);

  useEffect(() => {
    if (open && showSearch && inputRef.current) {
      inputRef.current.focus();
    }
  }, [open, showSearch]);

  const selectedOption = options.find((opt) => opt.value === value);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    setInputValue(val);
    onSearch?.(val);
  };

  const handleSelect = (opt: Option) => {
    if (opt.disabled) return;
    if (opt.value !== value) {
      onChange?.(opt.value, opt);
    }
    setOpen(false);
    setInputValue("");
  };

  const displayValue = showSearch && open ? inputValue : selectedOption ? selectedOption.label : "";

  const openDropdown = () => {
    if (disabled) return;
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect();
      setDropdownPos({
        left: rect.left + window.scrollX,
        top: rect.bottom + window.scrollY,
        width: rect.width,
      });
      setOpen(true);
    }
  };

  return (
    <>
      <div
        className={`ant-select-custom${disabled ? " ant-select-custom-disabled" : ""}${
          open ? " ant-select-custom-open" : ""
        }`}
        tabIndex={disabled ? -1 : 0}
        ref={ref}
        style={style}
        onClick={openDropdown}
      >
        <div className="ant-select-custom-selection">
          {showSearch && open ? (
            <input
              ref={inputRef}
              className="ant-select-custom-input"
              value={displayValue}
              onChange={handleInputChange}
              placeholder={placeholder}
              disabled={disabled}
              onClick={(e) => e.stopPropagation()}
            />
          ) : selectedOption ? (
            selectedOption.label
          ) : (
            <span className="ant-select-custom-placeholder">{placeholder}</span>
          )}
          <span className="ant-select-custom-arrow" />
        </div>
      </div>
      {open &&
        ReactDOM.createPortal(
          <ul
            ref={dropdownRef}
            className="ant-select-custom-dropdown"
            style={{
              position: "fixed",
              left: dropdownPos.left,
              top: dropdownPos.top,
              width: dropdownPos.width,
              zIndex: 9999,
            }}
          >
            {loading ? (
              <li className="ant-select-custom-option ant-select-custom-option-disabled"><LoadingIcon /></li>
            ) : options.length === 0 ? (
              <li className="ant-select-custom-option ant-select-custom-option-disabled">无数据</li>
            ) : (
              options.map((opt) => (
                <li
                  key={opt.value}
                  className={`ant-select-custom-option${opt.disabled ? " ant-select-custom-option-disabled" : ""}${
                    opt.value === value ? " ant-select-custom-option-selected" : ""
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSelect(opt);
                  }}
                >
                  {opt.label}
                </li>
              ))
            )}
          </ul>,
          document.body
        )}
    </>
  );
};

