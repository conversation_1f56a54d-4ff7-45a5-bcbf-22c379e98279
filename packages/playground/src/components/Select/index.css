.ant-select {
  padding: 4px 11px;
  border-radius: 2px;
  border: 1px solid #d9d9d9;
  min-width: 120px;
  font-size: 14px;
  transition: border-color 0.2s;
  outline: none;
  background: #fff;
}
.ant-select:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.ant-select[disabled] {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

.ant-select-custom {
  width: 200px;
  min-width: 120px;
  font-size: 14px;
  position: relative;
  display: inline-block;
  outline: none;
  cursor: pointer;
}
.ant-select-custom-disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}
.ant-select-custom-selection {
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  padding: 4px 32px 4px 11px;
  background: #fff;
  position: relative;
  min-height: 32px;
  display: flex;
  align-items: center;
  transition: border-color 0.2s;
  box-sizing: border-box;
  line-height: 1.5;
}
.ant-select-custom:focus .ant-select-custom-selection,
.ant-select-custom-open .ant-select-custom-selection {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.ant-select-custom-placeholder {
  color: #bfbfbf;
  font-size: 14px;
  line-height: 1.5;
  user-select: none;
}
.ant-select-custom-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 6px solid #999;
  transform: translateY(-50%);
  pointer-events: none;
}
.ant-select-custom-dropdown {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  margin-top: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  padding: 4px 0;
  list-style: none;
  width: 100%;
  box-sizing: border-box;
}
.ant-select-custom-option {
  font-size: 14px;
  padding: 6px 10px;
  cursor: pointer;
  transition: background 0.2s;
}
.ant-select-custom-option:hover:not(.ant-select-custom-option-disabled) {
  background: #f5f5f5;
}
.ant-select-custom-option-selected {
  background: #e6f7ff;
  color: #1890ff;
}
.ant-select-custom-option-disabled {
  color: #bfbfbf;
  cursor: not-allowed;
}
.ant-select-custom-input {
  border: none;
  outline: none;
  width: 100%;
  font-size: 14px;
  background: transparent;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  line-height: 1.5;
  height: auto;
}

.ant-select-custom-input::placeholder,
.ant-select-custom-input::-webkit-input-placeholder {
  color: #bfbfbf;
  font-size: 14px;
  line-height: 1.5;
}
