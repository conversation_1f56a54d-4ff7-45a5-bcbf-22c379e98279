export const debounce = (fn: () => void) => {
  let timer: number | null = null;
  let count = 0;
  return () => {
    if (timer === null) {
      timer = window.setTimeout(() => {
        if (count > 0) fn();
        timer = null;
      });
      count = 0;
      fn();
    } else {
      count++;
    }
  };
};

// ???
export const standardDebounce = <A extends unknown[]>(fn: (...args: A) => void, delay = 0) => {
  let timer: number | null = null;
  return (...args: A) => {
    if (timer !== null) {
      clearTimeout(timer);
    }
    timer = window.setTimeout(() => {
      fn(...args);
      timer = null;
    }, delay);
  };
};
