import { useMemo } from "react";
import { useUserInfo } from "../services/user";
import { type TagResult } from "@hll/quantum-store";

export const useEditable = (tr: TagResult<unknown, unknown> | undefined) => {
  const { data: userInfo, loading, error } = useUserInfo();

  const data = useMemo(() => {
    if (!userInfo || !tr) return null;
    const user = userInfo.uniqId;
    if (tr.owner === user) return true;
    return tr.getAuthorizedUsers().includes(user);
  }, [userInfo, tr]);

  return { data, loading, error };
};
