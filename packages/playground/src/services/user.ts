import { useEffect, useState } from "react";
import { rejectNon200 } from "../utils/rejectNon200";
import { u2a, u2o, u2s } from "u2x";

const vanApi = "https://gateway-office-public.huolala.work/fe-van-api-svc";
const feishuMe = `${vanApi}/api/feishu?info=me`;
const feishuUserInfoUrl = `${vanApi}/api/feishu?info=user`;
const searchUserUrl = `${vanApi}/api/user?info=searchUser`;

export const getHllIdentifier = () => {
  const raw = document.cookie.match(/(?:^|;\s*)_hll_identifier=(.*?)(?=;|$)/)?.[1];
  if (raw) return decodeURIComponent(raw);
  return "";
};

export interface UserInfo {
  uniqId: string;
  name: string;
  avatar72?: string;
  openId?: string;
}

export interface Me extends UserInfo {
  description?: string;
  title?: string;
  department?: string;
}

let me: Me | Promise<Me> | null = null;

export const getUserInfo = async (): Promise<Me> => {
  if (me) return me;
  me = fetch(feishuMe, { credentials: "include" })
    .then(rejectNon200)
    .then((res) => res.json())
    .then((data) => {
      const { enName, avatar, name, openId, description, customAttrs } = u2o(data);
      const customAttrMap = Object.fromEntries(u2a(customAttrs, u2o).map((i) => [u2s(i.id), u2o(i.value)]));
      const department = customAttrMap["C-6837374483993460738"]?.text;
      const title = customAttrMap["C-6797636000211288065"]?.text;
      return {
        uniqId: u2s(enName) ?? "unknown",
        name: u2s(name) ?? "Unknown",
        avatar72: u2s(u2o(avatar).avatar72),
        openId: u2s(openId),
        description: u2s(description),
        title: u2s(title),
        department: u2s(department),
      };
    });
  me.then(
    (data) => {
      me = data;
    },
    () => {
      me = null;
    }
  );
  return me;
};

export const useUserInfo = () => {
  const cached = me && !(me instanceof Promise) ? me : undefined;
  const [led, updateLed] = useState<{ data?: Me; loading: boolean; error?: unknown }>({
    data: cached,
    loading: !cached,
    error: undefined,
  });

  useEffect(() => {
    if (cached) return;
    updateLed({ loading: true });
    getUserInfo().then(
      (data) => {
        updateLed({ data, loading: false });
      },
      (error) => {
        updateLed({ loading: false, error });
      }
    );
  }, [cached, updateLed]);

  return led;
};

const userInfoPromiseCache: Record<string, Promise<UserInfo | null>> = Object.create(null);

export const getFeishuUserInfoById = async <T extends readonly string[]>(uniqIds: T) => {
  const result: Record<string, UserInfo | null> = {};
  const uncachedIds = uniqIds.filter((id) => !userInfoPromiseCache[id]);
  if (uncachedIds.length > 0) {
    const body = new FormData();
    uncachedIds.forEach((id) => body.append("uniqIds", id));
    const fetchPromise = fetch(feishuUserInfoUrl, { method: "POST", body, credentials: "include" })
      .then(rejectNon200)
      .then((res) => res.json() as Promise<{ [key: string]: UserInfo }>);
    uncachedIds.forEach((id) => {
      userInfoPromiseCache[id] = fetchPromise
        .then((info) => info?.[id] || null)
        .catch(() => {
          delete userInfoPromiseCache[id];
          return null;
        });
    });
  }
  const allPromises = uniqIds.map(async (id) => {
    try {
      const user = await userInfoPromiseCache[id];
      result[id] = user || null;
    } catch {
      result[id] = null;
    }
  });
  await Promise.all(allPromises);
  return result as { [K in T[number]]: UserInfo | null };
};

export const searchUser = (keyword: string) => {
  return fetch(`${searchUserUrl}&keyword=${keyword}`, { credentials: "include" })
    .then(rejectNon200)
    .then((res) => res.json() as Promise<{ userName: string; userUniqId: string }[]>);
};
