import { TagStore, TagStoreType } from "@hll/quantum-store";
import { Bumper } from "../vendors/Bumper";

const store = new TagStore<number>(TagStoreType.Public, "quantum-playground/increment.json");

const iv = new BigUint64Array(new TextEncoder().encode("QuantumBoxBumper").buffer);

const bumper = new Bumper(iv);

export const verifyId = (id: string) => {
  try {
    return bumper.decrypt(id);
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const nextId = async () => {
  const id = await store.transaction(async ({ data, head }) => {
    let id;
    if (!head) {
      await store.upload(2, "null");
      id = 1;
    } else {
      if (typeof data !== "number")
        throw new Error("OMG! The ID manager service is now broken, contact administrator to fix it");
      await store.upload(data + 1, head);
      id = data;
    }
    return bumper.encrypt(BigInt(id));
  });
  return id;
};
